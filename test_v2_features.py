"""
Test script for InsightPulse v2.0 features.
Tests configurable models, search engine, and modern interface.
"""

import sys
import os
import asyncio
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_configuration():
    """Test model configuration loading"""
    print("🔧 Testing model configuration...")
    try:
        from utils.model_config import get_model_config, get_available_providers, get_provider_models
        
        config = get_model_config()
        
        # Test provider loading
        providers = config.get_model_providers()
        assert len(providers) >= 9, f"Expected at least 9 providers, got {len(providers)}"
        
        # Test specific providers
        assert "openai" in providers
        assert "anthropic" in providers
        assert "google" in providers
        assert "deepseek" in providers
        assert "openrouter" in providers
        
        # Test model retrieval
        openai_models = get_provider_models("openai")
        assert len(openai_models) > 0, "OpenAI should have models"
        assert "gpt-4o" in openai_models or "gpt-4o-mini" in openai_models
        
        # Test available providers
        available = get_available_providers()
        assert len(available) > 0, "Should have at least demo mode"
        
        print(f"✅ Model configuration working")
        print(f"   Total providers: {len(providers)}")
        print(f"   Available providers: {len(available)}")
        print(f"   OpenAI models: {len(openai_models)}")
        
        return True
    except Exception as e:
        print(f"❌ Model configuration test failed: {e}")
        return False

def test_search_engine():
    """Test search engine functionality"""
    print("\n🔍 Testing search engine...")
    try:
        from core.search_engine import create_search_engine, SearchQuery, create_competitor_search_query
        
        # Test search engine creation
        search_engine = create_search_engine()
        assert search_engine is not None
        
        # Test available sources
        sources = search_engine.get_available_sources()
        print(f"   Available search sources: {sources}")
        
        # Test search query creation
        query = SearchQuery(
            query="note taking app",
            language="en",
            max_results=5,
            sources=["web"]
        )
        assert query.query == "note taking app"
        assert query.language == "en"
        
        # Test competitor search query
        competitor_query = create_competitor_search_query(["note", "app"], "en")
        assert "alternative" in competitor_query.query or "competitor" in competitor_query.query
        
        print("✅ Search engine structure working")
        print(f"   Search sources configured: {len(sources)}")
        
        return True
    except Exception as e:
        print(f"❌ Search engine test failed: {e}")
        return False

async def test_async_search():
    """Test async search functionality"""
    print("\n⚡ Testing async search...")
    try:
        from core.search_engine import create_search_engine, SearchQuery
        
        search_engine = create_search_engine()
        
        # Create a simple search query
        query = SearchQuery(
            query="productivity tools",
            language="en",
            max_results=3,
            sources=["web"]  # Only test web search
        )
        
        # Perform search (will use demo/fallback if no API keys)
        start_time = time.time()
        response = await search_engine.search(query)
        search_time = time.time() - start_time
        
        assert response is not None
        print(f"   Search completed in {search_time:.2f}s")
        print(f"   Success: {response.success}")
        print(f"   Results: {response.total_results}")
        print(f"   Sources used: {response.sources_used}")
        
        return True
    except Exception as e:
        print(f"❌ Async search test failed: {e}")
        return False

def test_gradio_components():
    """Test modern Gradio components"""
    print("\n🎨 Testing Gradio components...")
    try:
        import gradio as gr
        
        # Test modern components
        with gr.Blocks() as demo:
            # Test Tabs
            with gr.Tabs():
                with gr.Tab("Tab 1"):
                    gr.Textbox(label="Test")
                with gr.Tab("Tab 2"):
                    gr.Markdown("Test markdown")
            
            # Test CheckboxGroup
            checkbox_group = gr.CheckboxGroup(
                choices=[("Option 1", "opt1"), ("Option 2", "opt2")],
                label="Test Checkbox Group"
            )
            
            # Test Slider
            slider = gr.Slider(
                minimum=0,
                maximum=100,
                value=50,
                label="Test Slider"
            )
            
            # Test modern Button
            button = gr.Button("Test Button", variant="primary", size="lg")
        
        print(f"✅ Gradio components working")
        print(f"   Gradio version: {gr.__version__}")
        
        return True
    except Exception as e:
        print(f"❌ Gradio components test failed: {e}")
        return False

def test_ui_texts():
    """Test UI text generation"""
    print("\n🌍 Testing UI texts...")
    try:
        from app_v2 import get_ui_texts
        
        # Test English texts
        en_texts = get_ui_texts("en")
        assert "InsightPulse" in en_texts["title"]
        assert "Analysis Mode" in en_texts["mode_label"]
        
        # Test Chinese texts
        zh_texts = get_ui_texts("zh")
        assert "逛逛" in zh_texts["title"]
        assert "分析模式" in zh_texts["mode_label"]
        
        print("✅ UI texts working")
        print(f"   English keys: {len(en_texts)}")
        print(f"   Chinese keys: {len(zh_texts)}")
        
        return True
    except Exception as e:
        print(f"❌ UI texts test failed: {e}")
        return False

def test_analysis_simulation():
    """Test analysis simulation"""
    print("\n🤖 Testing analysis simulation...")
    try:
        from app_v2 import simulate_ai_analysis
        from core.ai_processor import AnalysisRequest
        
        # Create test request
        request = AnalysisRequest(
            user_input="Recommend a note-taking app",
            language="en",
            mode="user",
            provider="demo",
            model="demo-analyzer"
        )
        
        # Test async simulation
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(simulate_ai_analysis(request))
            assert len(result) > 0
            assert "Analysis Results" in result
            
            print("✅ Analysis simulation working")
            print(f"   Result length: {len(result)} characters")
            
            return True
        finally:
            loop.close()
            
    except Exception as e:
        print(f"❌ Analysis simulation test failed: {e}")
        return False

def test_configuration_files():
    """Test configuration files"""
    print("\n📁 Testing configuration files...")
    try:
        import json
        
        # Test models.json
        with open("config/models.json", "r", encoding="utf-8") as f:
            models_config = json.load(f)
        
        assert "model_providers" in models_config
        assert "search_engines" in models_config
        assert "data_sources" in models_config
        
        providers = models_config["model_providers"]
        assert len(providers) >= 9
        
        # Check each provider has required fields
        for provider_id, provider_info in providers.items():
            assert "name" in provider_info
            assert "models" in provider_info
            assert "env_key" in provider_info
            assert "description" in provider_info
            assert len(provider_info["models"]) > 0
        
        print("✅ Configuration files valid")
        print(f"   Model providers: {len(providers)}")
        print(f"   Search engines: {len(models_config['search_engines'])}")
        print(f"   Data sources: {len(models_config['data_sources'])}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration files test failed: {e}")
        return False

async def run_async_tests():
    """Run async tests"""
    return await test_async_search()

def main():
    """Run all v2.0 feature tests"""
    print("🚀 InsightPulse v2.0 Feature Tests")
    print("=" * 50)
    
    tests = [
        ("Model Configuration", test_model_configuration),
        ("Search Engine", test_search_engine),
        ("Gradio Components", test_gradio_components),
        ("UI Texts", test_ui_texts),
        ("Analysis Simulation", test_analysis_simulation),
        ("Configuration Files", test_configuration_files),
    ]
    
    passed = 0
    total = len(tests)
    
    # Run sync tests
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    # Run async tests
    print(f"\n{'='*20} Async Search {'='*20}")
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async_result = loop.run_until_complete(run_async_tests())
        if async_result:
            passed += 1
        
        loop.close()
        total += 1
    except Exception as e:
        print(f"❌ Async tests failed: {e}")
        total += 1
    
    # Print summary
    print("\n" + "="*60)
    print("📊 V2.0 FEATURE TEST RESULTS")
    print("="*60)
    
    print(f"📈 Overall Score: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL V2.0 FEATURES WORKING! 🎉")
        print("\n✅ New Features Status:")
        print("1. ✅ Configurable model providers (no hardcoded models)")
        print("2. ✅ Multi-source search engine integration")
        print("3. ✅ Modern Gradio interface with latest components")
        print("4. ✅ Async processing architecture")
        print("5. ✅ Comprehensive configuration management")
        print("6. ✅ Real-time search capabilities")
        
        print("\n📋 Ready for:")
        print("- Production deployment with API keys")
        print("- Real-time competitor analysis")
        print("- Multi-language support")
        print("- Advanced search integration")
        
    elif passed >= total * 0.8:
        print("\n✅ Most v2.0 features working!")
        print("🔧 Minor issues need attention.")
        
    else:
        print("\n❌ Multiple v2.0 features need work.")
        print("🛠️ Significant development required.")
    
    print(f"\n🏁 Test completed at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
