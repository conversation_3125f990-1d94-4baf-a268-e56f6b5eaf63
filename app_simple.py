"""
InsightPulse - Simplified version to avoid Gradio issues
AI-Powered Demand Exploration & Feasibility Analysis Platform
"""

import gradio as gr
import os
import time
from typing import Dict, List, Tuple

# Import our modules
from utils.config import get_config
from utils.logger import get_logger
from utils.model_config import get_available_providers, get_provider_models
from i18n.loader import set_language

# Initialize components
config = get_config()
logger = get_logger("InsightPulse")

def analyze_demand_simple(user_input: str, mode: str, language: str, provider: str, model: str) -> Tuple[str, str]:
    """Simplified analysis function"""
    
    if not user_input.strip():
        error_msg = "请输入您的需求描述" if language == "zh" else "Please enter your requirement description"
        return error_msg, ""
    
    try:
        set_language(language)
        
        # Simulate processing
        time.sleep(1)
        
        if language == "zh":
            analysis_result = f"""
## 📋 AI分析结果

**AI分析引擎**: {provider} - {model}

**用户意图**: {user_input}

**需求领域**: 效率工具

**关键词**: 笔记, 软件, 推荐

**用户模式**: {"开发者模式" if mode == "developer" else "普通用户模式"}

**分析置信度**: 85%

---
*AI分析完成 - 演示版本*
"""
            
            recommendations = f"""
## 💡 推荐建议

### 🔍 现有解决方案推荐

1. **印象笔记 (Evernote)**
   - 评分: 4.5/5
   - 核心功能: 全平台同步、网页剪藏、OCR识别
   - 优势: 功能全面、生态成熟

2. **Notion**
   - 评分: 4.7/5
   - 核心功能: 块编辑器、数据库、协作
   - 优势: 高度灵活、协作友好

3. **Obsidian**
   - 评分: 4.6/5
   - 核心功能: 双向链接、本地存储、插件生态
   - 优势: 本地优先、双向链接强大

*基于AI分析的个性化推荐*
"""
        else:
            analysis_result = f"""
## 📋 AI Analysis Results

**AI Analysis Engine**: {provider} - {model}

**User Intent**: {user_input}

**Domain Category**: Productivity Tools

**Keywords**: note, software, recommend

**User Mode**: {"Developer Mode" if mode == "developer" else "User Mode"}

**Confidence Score**: 85%

---
*AI Analysis Completed - Demo Version*
"""
            
            recommendations = f"""
## 💡 Recommendations

### 🔍 Existing Solution Recommendations

1. **Evernote**
   - Rating: 4.5/5
   - Core Features: Cross-platform sync, web clipper, OCR
   - Pros: Comprehensive features, mature ecosystem

2. **Notion**
   - Rating: 4.7/5
   - Features: Block editor, databases, collaboration
   - Pros: Highly flexible, collaboration-friendly

3. **Obsidian**
   - Rating: 4.6/5
   - Features: Bidirectional links, local storage, plugins
   - Pros: Local-first, powerful linking

*Personalized recommendations based on AI analysis*
"""
        
        return analysis_result, recommendations
        
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        error_msg = "分析过程中出现错误" if language == "zh" else "Error occurred during analysis"
        return error_msg, ""

def create_simple_interface():
    """Create simplified Gradio interface"""
    
    with gr.Blocks(title="InsightPulse", theme=gr.themes.Default()) as app:
        
        # Language state
        current_language = gr.State("en")
        
        # Header
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin-bottom: 20px;">
            <h1>🔍 InsightPulse</h1>
            <p>AI-Powered Demand Exploration & Feasibility Analysis Platform</p>
        </div>
        """)
        
        # Language toggle
        with gr.Row():
            language_btn = gr.Button("🌐 中文", size="sm")
        
        # Main interface
        with gr.Row():
            with gr.Column():
                # Mode selector
                mode = gr.Radio(
                    choices=[
                        ("🔍 User Mode - Find existing solutions", "user"),
                        ("💡 Developer Mode - Explore innovation opportunities", "developer")
                    ],
                    value="user",
                    label="Analysis Mode"
                )
                
                # Input
                user_input = gr.Textbox(
                    label="Requirement Description",
                    placeholder="Please describe your requirements or ideas in detail...\n\nExamples:\n- Recommend a good note-taking app\n- Develop an AI-driven code review tool",
                    lines=4
                )
                
            with gr.Column():
                # AI Model selection
                provider_dropdown = gr.Dropdown(
                    choices=get_available_providers(),
                    value="demo",
                    label="AI Model Provider"
                )
                
                model_dropdown = gr.Dropdown(
                    choices=["demo-analyzer"],
                    value="demo-analyzer",
                    label="Select Model"
                )
        
        # Action buttons
        with gr.Row():
            submit_btn = gr.Button("🚀 Start AI Analysis", variant="primary")
            clear_btn = gr.Button("🗑️ Clear")
        
        # Examples
        gr.Examples(
            examples=[
                ["Recommend a good note-taking app", "user"],
                ["Find an online collaboration tool", "user"],
                ["Develop an AI-driven code review tool", "developer"],
                ["Create an intelligent customer service chatbot", "developer"]
            ],
            inputs=[user_input, mode],
            label="Examples"
        )
        
        # Results
        with gr.Row():
            with gr.Column():
                analysis_output = gr.Markdown(
                    label="📋 AI Analysis Results",
                    value="Click 'Start AI Analysis' to see results..."
                )
            with gr.Column():
                recommendations_output = gr.Markdown(
                    label="💡 Recommendations", 
                    value="Analysis results will appear here..."
                )
        
        # Event handlers
        def switch_language(current_lang):
            new_lang = "zh" if current_lang == "en" else "en"
            btn_text = "🌐 English" if new_lang == "zh" else "🌐 中文"
            return new_lang, btn_text
        
        def update_models(provider):
            models = get_provider_models(provider)
            model_choices = [(model, model) for model in models]
            return gr.update(choices=model_choices, value=models[0] if models else None)
        
        # Wire up events
        language_btn.click(
            fn=switch_language,
            inputs=[current_language],
            outputs=[current_language, language_btn]
        )
        
        provider_dropdown.change(
            fn=update_models,
            inputs=[provider_dropdown],
            outputs=[model_dropdown]
        )
        
        submit_btn.click(
            fn=analyze_demand_simple,
            inputs=[user_input, mode, current_language, provider_dropdown, model_dropdown],
            outputs=[analysis_output, recommendations_output]
        )
        
        clear_btn.click(
            fn=lambda: ("", "", ""),
            outputs=[user_input, analysis_output, recommendations_output]
        )
        
        # Footer
        gr.HTML("""
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <p><strong>🚀 Powered by</strong></p>
            <p>Gradio • Python • AI Models</p>
            <p><em>HF Hackathon Project</em></p>
        </div>
        """)
    
    return app

def main():
    """Main function"""
    logger.info("Starting InsightPulse...")
    
    app = create_simple_interface()
    
    # Launch with basic configuration
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )

if __name__ == "__main__":
    main()
