"""
Test script for AI processing chain.
Tests the complete AI analysis workflow.
"""

import sys
import os
import asyncio
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ai_processor_import():
    """Test AI processor import"""
    print("🤖 Testing AI processor import...")
    try:
        from core.ai_processor import AIProcessor, AnalysisRequest, AnalysisResult
        from core.ai_processor import CompetitorInfo, FeasibilityScore, InnovationOpportunity
        from core.ai_processor import format_analysis_result_for_ui
        
        print("✅ AI processor classes imported successfully")
        return True
    except Exception as e:
        print(f"❌ AI processor import failed: {e}")
        return False

def test_analysis_request():
    """Test analysis request creation"""
    print("\n📋 Testing analysis request...")
    try:
        from core.ai_processor import AnalysisRequest
        
        request = AnalysisRequest(
            user_input="Recommend a good note-taking app",
            language="en",
            mode="user",
            provider="demo",
            model="demo-analyzer"
        )
        
        assert request.user_input == "Recommend a good note-taking app"
        assert request.language == "en"
        assert request.mode == "user"
        
        print("✅ Analysis request created successfully")
        return True
    except Exception as e:
        print(f"❌ Analysis request test failed: {e}")
        return False

def test_ai_processor_creation():
    """Test AI processor creation"""
    print("\n🏭 Testing AI processor creation...")
    try:
        from llm.base import LLMManager
        from core.ai_processor import AIProcessor
        
        llm_manager = LLMManager()
        ai_processor = AIProcessor(llm_manager)
        
        assert ai_processor.llm_manager is not None
        assert ai_processor.demand_parser is not None
        
        print("✅ AI processor created successfully")
        return True
    except Exception as e:
        print(f"❌ AI processor creation failed: {e}")
        return False

async def test_async_processing():
    """Test async processing workflow"""
    print("\n⚡ Testing async processing...")
    try:
        from llm.base import LLMManager
        from core.ai_processor import AIProcessor, AnalysisRequest
        
        llm_manager = LLMManager()
        ai_processor = AIProcessor(llm_manager)
        
        request = AnalysisRequest(
            user_input="推荐一个好用的笔记软件",
            language="zh",
            mode="user",
            provider="demo",
            model="demo-analyzer"
        )
        
        # Test async processing (will use fallback since no real LLM)
        result = await ai_processor.process_request(request)
        
        assert result is not None
        assert result.request.user_input == request.user_input
        assert result.demand_analysis is not None
        
        print(f"✅ Async processing completed in {result.processing_time:.3f}s")
        print(f"   User mode: {result.demand_analysis.user_mode}")
        print(f"   Category: {result.demand_analysis.category}")
        print(f"   Success: {result.success}")
        
        return True
    except Exception as e:
        print(f"❌ Async processing test failed: {e}")
        return False

def test_ui_formatting():
    """Test UI result formatting"""
    print("\n🎨 Testing UI formatting...")
    try:
        from core.ai_processor import format_analysis_result_for_ui, AnalysisResult, AnalysisRequest
        from core.demand_parser import DemandAnalysis, UserMode, DemandCategory
        
        # Create mock result
        request = AnalysisRequest(
            user_input="Test input",
            language="en",
            mode="user",
            provider="demo",
            model="demo-analyzer"
        )
        
        demand_analysis = DemandAnalysis(
            user_intent="Test intent",
            category=DemandCategory.PRODUCTIVITY,
            keywords=["test", "keyword"],
            constraints=[],
            user_mode=UserMode.USER,
            confidence_score=0.8,
            language="en",
            raw_input="Test input"
        )
        
        result = AnalysisResult(
            request=request,
            demand_analysis=demand_analysis,
            competitors=[],
            innovation_opportunity=None,
            processing_time=1.5,
            success=True
        )
        
        # Test formatting
        analysis_text, recommendations_text, visualization_text = format_analysis_result_for_ui(result, "en")
        
        assert "Analysis Results" in analysis_text
        assert len(recommendations_text) > 0
        assert len(visualization_text) > 0
        
        print("✅ UI formatting working correctly")
        print(f"   Analysis text length: {len(analysis_text)}")
        print(f"   Recommendations length: {len(recommendations_text)}")
        print(f"   Visualization length: {len(visualization_text)}")
        
        return True
    except Exception as e:
        print(f"❌ UI formatting test failed: {e}")
        return False

def test_model_providers():
    """Test model provider configuration"""
    print("\n🔧 Testing model providers...")
    try:
        from app_international import MODEL_PROVIDERS, get_available_providers, get_models_for_provider
        
        # Test provider configuration
        assert len(MODEL_PROVIDERS) >= 9  # We added 9 providers
        assert "openai" in MODEL_PROVIDERS
        assert "anthropic" in MODEL_PROVIDERS
        assert "google" in MODEL_PROVIDERS
        assert "deepseek" in MODEL_PROVIDERS
        assert "openrouter" in MODEL_PROVIDERS
        
        # Test available providers
        available = get_available_providers()
        assert len(available) > 0  # At least demo mode should be available
        
        # Test model retrieval
        openai_models = get_models_for_provider("openai")
        assert len(openai_models) > 0
        assert "gpt-4o" in openai_models
        
        demo_models = get_models_for_provider("demo")
        assert "demo-analyzer" in demo_models
        
        print("✅ Model providers configured correctly")
        print(f"   Total providers: {len(MODEL_PROVIDERS)}")
        print(f"   Available providers: {len(available)}")
        print(f"   OpenAI models: {len(openai_models)}")
        
        return True
    except Exception as e:
        print(f"❌ Model providers test failed: {e}")
        return False

def test_interface_language_switching():
    """Test interface language switching"""
    print("\n🌍 Testing language switching...")
    try:
        from app_international import update_interface_language
        
        # Test Chinese
        zh_texts = update_interface_language("zh")
        assert "逛逛" in zh_texts["title"]
        assert "使用模式" in zh_texts["mode_label"]
        
        # Test English
        en_texts = update_interface_language("en")
        assert "InsightPulse" in en_texts["title"]
        assert "Mode" in en_texts["mode_label"]
        
        print("✅ Language switching working correctly")
        print(f"   Chinese title: {zh_texts['title']}")
        print(f"   English title: {en_texts['title']}")
        
        return True
    except Exception as e:
        print(f"❌ Language switching test failed: {e}")
        return False

async def run_async_tests():
    """Run async tests"""
    return await test_async_processing()

def main():
    """Run all AI chain tests"""
    print("🎯 InsightPulse AI Processing Chain Tests")
    print("=" * 50)
    
    tests = [
        ("AI Processor Import", test_ai_processor_import),
        ("Analysis Request", test_analysis_request),
        ("AI Processor Creation", test_ai_processor_creation),
        ("UI Formatting", test_ui_formatting),
        ("Model Providers", test_model_providers),
        ("Language Switching", test_interface_language_switching),
    ]
    
    passed = 0
    total = len(tests)
    
    # Run sync tests
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    # Run async tests
    print(f"\n{'='*20} Async Processing {'='*20}")
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        async_result = loop.run_until_complete(run_async_tests())
        if async_result:
            passed += 1
        
        loop.close()
        total += 1
    except Exception as e:
        print(f"❌ Async tests failed: {e}")
        total += 1
    
    # Print summary
    print("\n" + "="*60)
    print("📊 AI CHAIN TEST RESULTS")
    print("="*60)
    
    print(f"📈 Overall Score: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL AI CHAIN TESTS PASSED! 🎉")
        print("\n✅ AI Processing Chain Status:")
        print("1. ✅ Core AI processor architecture working")
        print("2. ✅ Async processing workflow functional")
        print("3. ✅ UI formatting and display ready")
        print("4. ✅ Multiple AI provider support configured")
        print("5. ✅ International language switching operational")
        print("\n📋 Ready for:")
        print("- Real AI API integration")
        print("- Production deployment")
        print("- User testing and feedback")
        
    elif passed >= total * 0.8:
        print("\n✅ Most AI chain tests passed!")
        print("🔧 Minor issues need attention before full deployment.")
        
    else:
        print("\n❌ Multiple AI chain tests failed.")
        print("🛠️ Significant issues need resolution.")
    
    print(f"\n🏁 Test completed at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
