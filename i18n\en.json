{"app": {"title": "InsightPulse - Demand Exploration & Feasibility Analysis Platform", "subtitle": "Connecting Needs with Solutions, Empowering Innovation Decisions", "description": "Intelligently analyze your requirements, discover existing solutions, and evaluate innovation opportunities"}, "ui": {"language_selector": "Language", "mode_selector": "Mode", "input_placeholder": "Please describe your requirements or ideas...", "submit_button": "Start Analysis", "clear_button": "Clear", "export_button": "Export Report", "loading": "Analyzing, please wait...", "error": "An error occurred during analysis, please try again", "no_results": "No relevant results found"}, "modes": {"user": {"name": "User Mode", "description": "Find existing solutions for specific needs", "icon": "🔍"}, "developer": {"name": "Developer Mode", "description": "Explore innovation opportunities and assess project feasibility", "icon": "💡"}}, "analysis": {"demand_parsing": {"title": "Demand Analysis", "intent": "User Intent", "category": "Domain Category", "keywords": "Keywords", "constraints": "Constraints"}, "ecosystem_scan": {"title": "Ecosystem Scan", "existing_solutions": "Existing Solutions", "top_recommendations": "Top Recommendations", "competitor_analysis": "Competitor Analysis", "market_overview": "Market Overview"}, "innovation_radar": {"title": "Innovation Radar", "feasibility": "Technical Feasibility", "market_demand": "Market Demand", "competition": "Competition Level", "legal_risk": "Legal Risk", "cost_control": "Cost Control", "market_acceptance": "Market Acceptance"}, "case_analysis": {"title": "Historical Case Analysis", "similar_projects": "Similar Projects", "success_rate": "Success Rate", "failure_reasons": "Failure Reasons", "key_factors": "Key Factors", "lessons_learned": "Lessons Learned"}, "decision_engine": {"title": "Decision Recommendations", "recommendation": "Recommended Action", "reasoning": "Analysis Reasoning", "next_steps": "Next Steps", "risk_warning": "Risk Warning"}}, "recommendations": {"abandon": {"icon": "🚫", "title": "Recommend Abandoning", "description": "High competition saturation with no clear differentiation opportunities"}, "proceed": {"icon": "💎", "title": "Recommend New Direction", "description": "Blue ocean opportunities or improvement points for existing solutions discovered"}, "high_risk": {"icon": "⚠️", "title": "High Risk Warning", "description": "Significant technical, legal, or market risks exist"}, "optimize": {"icon": "🔧", "title": "Optimization Suggestions", "description": "Existing solutions can gain competitive advantage through improvements"}}, "results": {"solution_card": {"rating": "Rating", "features": "Core Features", "pros": "Advantages", "cons": "Disadvantages", "pricing": "Pricing", "website": "Website", "last_updated": "Last Updated"}, "radar_chart": {"title": "Feasibility Radar Chart", "score": "Score"}, "export": {"pdf_title": "Demand Analysis Report", "generated_at": "Generated At", "user_query": "User Query", "analysis_summary": "Analysis Summary"}}, "errors": {"api_error": "API call failed, please try again later", "network_error": "Network connection error", "timeout_error": "Request timeout, please retry", "invalid_input": "Invalid input, please check and retry", "service_unavailable": "Service temporarily unavailable", "rate_limit": "Too many requests, please try again later"}, "footer": {"powered_by": "Powered by", "version": "Version", "contact": "Contact Us", "privacy": "Privacy Policy", "terms": "Terms of Service"}, "examples": {"user_mode": ["What are some good note-taking apps", "Recommend an online collaboration tool", "Looking for a code editor", "Need a project management tool"], "developer_mode": ["Develop an AI-driven code review tool", "Create an intelligent customer service chatbot", "Build a blockchain voting system", "Design an AR shopping application"]}, "tips": {"input_tips": "💡 Tip: The more detailed the description, the more accurate the analysis", "mode_tips": "Choose the appropriate mode for optimal analysis results", "language_tips": "Supports Chinese-English switching, content will be automatically adapted"}}