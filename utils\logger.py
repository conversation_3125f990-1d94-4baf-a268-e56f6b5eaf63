"""
Logging utilities for InsightPulse platform.
Provides structured logging with different levels and contexts.
"""

import logging
import sys
from typing import Optional, Dict, Any
from datetime import datetime
import json
import traceback
from functools import wraps

class StructuredLogger:
    """Structured logger with context support"""
    
    def __init__(self, name: str, level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, level.upper()))
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler for errors
        error_handler = logging.FileHandler('errors.log')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        self.logger.addHand<PERSON>(error_handler)
    
    def _format_message(self, message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Format message with context"""
        if context:
            context_str = json.dumps(context, default=str, ensure_ascii=False)
            return f"{message} | Context: {context_str}"
        return message
    
    def debug(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Log debug message"""
        self.logger.debug(self._format_message(message, context))
    
    def info(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Log info message"""
        self.logger.info(self._format_message(message, context))
    
    def warning(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Log warning message"""
        self.logger.warning(self._format_message(message, context))
    
    def error(self, message: str, context: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """Log error message"""
        self.logger.error(self._format_message(message, context), exc_info=exc_info)
    
    def critical(self, message: str, context: Optional[Dict[str, Any]] = None):
        """Log critical message"""
        self.logger.critical(self._format_message(message, context))

class APILogger(StructuredLogger):
    """Specialized logger for API calls"""
    
    def log_api_request(self, provider: str, endpoint: str, method: str = "POST", 
                       request_data: Optional[Dict] = None):
        """Log API request"""
        context = {
            "provider": provider,
            "endpoint": endpoint,
            "method": method,
            "timestamp": datetime.now().isoformat()
        }
        if request_data:
            # Don't log sensitive data
            safe_data = {k: v for k, v in request_data.items() 
                        if k not in ['api_key', 'token', 'password']}
            context["request_size"] = len(str(safe_data))
        
        self.info(f"API Request to {provider}", context)
    
    def log_api_response(self, provider: str, endpoint: str, status_code: int, 
                        response_time: float, success: bool = True):
        """Log API response"""
        context = {
            "provider": provider,
            "endpoint": endpoint,
            "status_code": status_code,
            "response_time_ms": round(response_time * 1000, 2),
            "success": success,
            "timestamp": datetime.now().isoformat()
        }
        
        if success:
            self.info(f"API Response from {provider}", context)
        else:
            self.error(f"API Error from {provider}", context)
    
    def log_api_error(self, provider: str, endpoint: str, error: Exception, 
                     retry_count: int = 0):
        """Log API error"""
        context = {
            "provider": provider,
            "endpoint": endpoint,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "retry_count": retry_count,
            "timestamp": datetime.now().isoformat()
        }
        
        self.error(f"API Error: {provider} - {endpoint}", context, exc_info=True)

def log_execution_time(logger: StructuredLogger):
    """Decorator to log function execution time"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = datetime.now()
            try:
                result = func(*args, **kwargs)
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                
                logger.debug(
                    f"Function {func.__name__} executed successfully",
                    {"execution_time_seconds": execution_time}
                )
                return result
            except Exception as e:
                end_time = datetime.now()
                execution_time = (end_time - start_time).total_seconds()
                
                logger.error(
                    f"Function {func.__name__} failed",
                    {
                        "execution_time_seconds": execution_time,
                        "error": str(e)
                    },
                    exc_info=True
                )
                raise
        return wrapper
    return decorator

def log_user_action(logger: StructuredLogger):
    """Decorator to log user actions"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Extract user context if available
            context = {
                "action": func.__name__,
                "timestamp": datetime.now().isoformat()
            }
            
            # Try to extract user info from kwargs
            if 'user_id' in kwargs:
                context['user_id'] = kwargs['user_id']
            if 'language' in kwargs:
                context['language'] = kwargs['language']
            
            logger.info(f"User action: {func.__name__}", context)
            
            try:
                result = func(*args, **kwargs)
                logger.debug(f"User action {func.__name__} completed successfully")
                return result
            except Exception as e:
                logger.error(
                    f"User action {func.__name__} failed",
                    {"error": str(e)},
                    exc_info=True
                )
                raise
        return wrapper
    return decorator

# Global logger instances
app_logger = StructuredLogger("InsightPulse")
api_logger = APILogger("InsightPulse.API")
llm_logger = APILogger("InsightPulse.LLM")

def get_logger(name: str) -> StructuredLogger:
    """Get a logger instance"""
    return StructuredLogger(name)

def get_api_logger(name: str) -> APILogger:
    """Get an API logger instance"""
    return APILogger(name)
