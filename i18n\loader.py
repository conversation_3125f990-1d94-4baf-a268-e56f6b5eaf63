"""
Internationalization (i18n) loader for InsightPulse platform.
Handles loading and managing multilingual resources.
"""

import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.config import get_config
from utils.logger import get_logger

logger = get_logger("I18nLoader")

class I18nLoader:
    """Internationalization resource loader"""
    
    def __init__(self, i18n_dir: str = "i18n"):
        self.i18n_dir = Path(i18n_dir)
        self.resources: Dict[str, Dict[str, Any]] = {}
        self.current_language = get_config().app.default_language
        self.supported_languages = get_config().app.get_supported_languages()
        
        # Load all language resources
        self._load_all_resources()
    
    def _load_all_resources(self):
        """Load all language resource files"""
        try:
            for lang in self.supported_languages:
                resource_file = self.i18n_dir / f"{lang}.json"
                if resource_file.exists():
                    with open(resource_file, 'r', encoding='utf-8') as f:
                        self.resources[lang] = json.load(f)
                    logger.info(f"Loaded {lang} language resources")
                else:
                    logger.warning(f"Language resource file not found: {resource_file}")
        except Exception as e:
            logger.error(f"Failed to load language resources: {e}")
            # Fallback to minimal English resources
            self._load_fallback_resources()
    
    def _load_fallback_resources(self):
        """Load minimal fallback resources"""
        fallback = {
            "app": {
                "title": "InsightPulse",
                "subtitle": "Demand Exploration Platform"
            },
            "ui": {
                "loading": "Loading...",
                "error": "Error occurred",
                "submit_button": "Submit"
            }
        }
        self.resources["en"] = fallback
        self.current_language = "en"
        logger.info("Loaded fallback language resources")
    
    def set_language(self, language: str) -> bool:
        """
        Set current language
        
        Args:
            language: Language code (e.g., 'zh', 'en')
            
        Returns:
            bool: True if language was set successfully
        """
        if language in self.resources:
            self.current_language = language
            logger.info(f"Language set to: {language}")
            return True
        else:
            logger.warning(f"Language not supported: {language}")
            return False
    
    def get_text(self, key: str, language: Optional[str] = None, **kwargs) -> str:
        """
        Get localized text by key
        
        Args:
            key: Dot-separated key path (e.g., 'ui.submit_button')
            language: Language code (uses current language if None)
            **kwargs: Variables for string formatting
            
        Returns:
            str: Localized text or key if not found
        """
        lang = language or self.current_language
        
        # Get resource for language, fallback to English, then to key itself
        resource = self.resources.get(lang, self.resources.get('en', {}))
        
        # Navigate through nested keys
        keys = key.split('.')
        value = resource
        
        try:
            for k in keys:
                value = value[k]
            
            # Format string with provided variables
            if isinstance(value, str) and kwargs:
                return value.format(**kwargs)
            
            return str(value)
        except (KeyError, TypeError):
            logger.warning(f"Translation key not found: {key} for language: {lang}")
            return key
    
    def get_dict(self, key: str, language: Optional[str] = None) -> Dict[str, Any]:
        """
        Get localized dictionary by key
        
        Args:
            key: Dot-separated key path
            language: Language code (uses current language if None)
            
        Returns:
            Dict: Localized dictionary or empty dict if not found
        """
        lang = language or self.current_language
        resource = self.resources.get(lang, self.resources.get('en', {}))
        
        keys = key.split('.')
        value = resource
        
        try:
            for k in keys:
                value = value[k]
            
            if isinstance(value, dict):
                return value
            else:
                return {}
        except (KeyError, TypeError):
            logger.warning(f"Translation dict not found: {key} for language: {lang}")
            return {}
    
    def get_list(self, key: str, language: Optional[str] = None) -> List[Any]:
        """
        Get localized list by key
        
        Args:
            key: Dot-separated key path
            language: Language code (uses current language if None)
            
        Returns:
            List: Localized list or empty list if not found
        """
        lang = language or self.current_language
        resource = self.resources.get(lang, self.resources.get('en', {}))
        
        keys = key.split('.')
        value = resource
        
        try:
            for k in keys:
                value = value[k]
            
            if isinstance(value, list):
                return value
            else:
                return []
        except (KeyError, TypeError):
            logger.warning(f"Translation list not found: {key} for language: {lang}")
            return []
    
    def get_available_languages(self) -> List[str]:
        """Get list of available languages"""
        return list(self.resources.keys())
    
    def get_language_name(self, language: str) -> str:
        """Get display name for language"""
        language_names = {
            'zh': '中文',
            'en': 'English',
            'ja': '日本語',
            'ko': '한국어',
            'fr': 'Français',
            'de': 'Deutsch',
            'es': 'Español'
        }
        return language_names.get(language, language)
    
    def reload_resources(self):
        """Reload all language resources"""
        self.resources.clear()
        self._load_all_resources()
        logger.info("Language resources reloaded")

# Global i18n loader instance
i18n = I18nLoader()

def get_i18n() -> I18nLoader:
    """Get the global i18n loader instance"""
    return i18n

def t(key: str, language: Optional[str] = None, **kwargs) -> str:
    """
    Shorthand function for getting localized text
    
    Args:
        key: Translation key
        language: Language code
        **kwargs: Variables for string formatting
        
    Returns:
        str: Localized text
    """
    return i18n.get_text(key, language, **kwargs)

def set_language(language: str) -> bool:
    """
    Shorthand function for setting language
    
    Args:
        language: Language code
        
    Returns:
        bool: True if successful
    """
    return i18n.set_language(language)
