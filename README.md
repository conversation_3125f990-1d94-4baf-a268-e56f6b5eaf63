# 逛逛 (InsightPulse) - 需求探索与可行性分析平台

[![Hugging Face Spaces](https://img.shields.io/badge/%F0%9F%A4%97%20Hugging%20Face-Spaces-blue)](https://huggingface.co/spaces/explorer-agent)
[![Python](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Gradio](https://img.shields.io/badge/gradio-5.x-orange.svg)](https://gradio.app/)

## 🎯 项目概述

逛逛（InsightPulse）是一个智能需求探索与可行性分析平台，旨在连接潜在用户需求与可行的解决方案，帮助创新者、开发者及普通用户识别市场机会、评估创意可行性、规避重复建设。

### 核心功能

- 🔍 **需求解析引擎**: 智能理解用户需求，识别用户模式和领域分类
- 🌐 **生态扫描**: 多源数据聚合，自动发现现有解决方案和竞品
- 📊 **创新雷达**: 多维度风险与机会评估，可视化展示
- 📚 **需求考古**: 历史案例分析，提取成功要素和失败教训
- 💡 **决策建议**: 综合分析结果，提供明确的行动建议

### 用户模式

- **普通用户模式**: 寻找满足特定需求的现有解决方案
- **开发者模式**: 探索创新机会，评估项目可行性

## 🚀 快速开始

### 环境要求

- Python 3.8+
- 支持的LLM API密钥（OpenAI、Claude、DeepSeek等）

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置环境变量

创建 `.env` 文件：

```env
# LLM API配置
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key

# 其他API配置
GITHUB_TOKEN=your_github_token
```

### 运行应用

```bash
python app.py
```

## 🏗️ 项目架构

```
insight-pulse/
├── app.py                 # Gradio主应用
├── core/                  # 核心业务逻辑
│   ├── __init__.py
│   ├── demand_parser.py   # 需求解析引擎
│   ├── ecosystem_scanner.py # 生态扫描引擎
│   ├── innovation_radar.py  # 创新雷达模块
│   ├── case_analyzer.py     # 需求考古模块
│   └── decision_engine.py   # 决策建议引擎
├── llm/                   # LLM接口层
│   ├── __init__.py
│   ├── base.py           # 基础LLM接口
│   ├── openai_client.py  # OpenAI接口
│   ├── anthropic_client.py # Anthropic接口
│   └── deepseek_client.py  # DeepSeek接口
├── data/                  # 数据管理
│   ├── __init__.py
│   ├── sources.py        # 数据源管理
│   └── cache.py          # 缓存管理
├── i18n/                 # 国际化资源
│   ├── zh.json          # 中文资源
│   ├── en.json          # 英文资源
│   └── loader.py        # 国际化加载器
├── utils/                # 工具模块
│   ├── __init__.py
│   ├── config.py        # 配置管理
│   ├── logger.py        # 日志管理
│   └── retry.py         # 重试机制
├── tests/                # 测试文件
├── requirements.txt      # 依赖列表
└── README.md            # 项目文档
```

## 🌍 国际化支持

平台支持中英文双语：
- 🇨🇳 中文（默认）
- 🇬🇧 English
- 🔄 动态语言切换

## 📝 使用示例

### 普通用户模式示例

**输入**: "有什么好用的笔记软件"

**输出**:
- 📱 推荐软件列表（印象笔记、Notion、Obsidian等）
- ⭐ 评分和核心优势
- 🔗 官网链接和下载地址

### 开发者模式示例

**输入**: "开发一个AI驱动的代码审查工具"

**输出**:
- 🔍 竞品分析（GitHub Copilot、CodeT5等）
- 📊 市场机会评估
- ⚠️ 技术风险分析
- 💡 差异化建议

## 🛠️ 技术栈

- **前端**: Gradio 5.x
- **后端**: Python 3.8+
- **LLM**: OpenAI GPT、Anthropic Claude、DeepSeek
- **可视化**: Plotly
- **部署**: Hugging Face Spaces

## 📊 API文档

详细的API文档请参考 [API Documentation](docs/api.md)

## 🤝 贡献指南

欢迎贡献代码！请查看 [Contributing Guidelines](CONTRIBUTING.md)

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢所有贡献者和开源社区的支持！

---

**开发团队**: HF黑客松参赛团队  
**联系方式**: [项目主页](https://huggingface.co/spaces/explorer-agent)
