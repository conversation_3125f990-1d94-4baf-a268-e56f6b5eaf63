"""
InsightPulse 配置设置工具
简化用户配置过程的交互式脚本
"""

import os
import shutil
from typing import Dict, List

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🔑 InsightPulse 配置设置工具")
    print("=" * 60)
    print()

def check_env_file():
    """检查.env文件是否存在"""
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            print("📋 创建配置文件...")
            shutil.copy('.env.example', '.env')
            print("✅ 已创建 .env 文件")
        else:
            print("❌ 找不到 .env.example 文件")
            return False
    else:
        print("✅ 找到现有的 .env 文件")
    return True

def get_user_choice():
    """获取用户选择的配置方案"""
    print("\n🚀 选择配置方案:")
    print("1. 演示模式 (无需API密钥，立即可用)")
    print("2. 基础AI模式 (需要OpenAI密钥)")
    print("3. 完整功能 (AI + 搜索)")
    print("4. 自定义配置")
    print("5. 查看当前配置")
    
    while True:
        choice = input("\n请选择 (1-5): ").strip()
        if choice in ['1', '2', '3', '4', '5']:
            return int(choice)
        print("❌ 请输入有效选项 (1-5)")

def update_env_file(updates: Dict[str, str]):
    """更新.env文件"""
    if not os.path.exists('.env'):
        print("❌ .env 文件不存在")
        return False
    
    # 读取现有内容
    with open('.env', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 更新配置
    updated_lines = []
    updated_keys = set()
    
    for line in lines:
        line = line.strip()
        if '=' in line and not line.startswith('#'):
            key = line.split('=')[0].strip()
            if key in updates:
                updated_lines.append(f"{key}={updates[key]}\n")
                updated_keys.add(key)
            else:
                updated_lines.append(line + '\n')
        else:
            updated_lines.append(line + '\n')
    
    # 添加新的配置项
    for key, value in updates.items():
        if key not in updated_keys:
            updated_lines.append(f"{key}={value}\n")
    
    # 写回文件
    with open('.env', 'w', encoding='utf-8') as f:
        f.writelines(updated_lines)
    
    return True

def setup_demo_mode():
    """设置演示模式"""
    print("\n🎯 演示模式配置")
    print("✅ 无需任何API密钥")
    print("✅ 所有功能都可以测试")
    print("✅ 使用模拟数据展示效果")
    print("\n配置完成！可以直接运行: python app.py")

def setup_basic_ai():
    """设置基础AI模式"""
    print("\n🤖 基础AI模式配置")
    print("需要OpenAI API密钥")
    
    api_key = input("请输入OpenAI API密钥 (sk-开头): ").strip()
    if not api_key:
        print("❌ 未输入API密钥，保持演示模式")
        return
    
    if not api_key.startswith('sk-'):
        print("⚠️  警告: OpenAI密钥通常以 'sk-' 开头")
    
    updates = {'OPENAI_API_KEY': api_key}
    if update_env_file(updates):
        print("✅ 配置已更新")
        print("✅ 现在可以使用真实的AI分析功能")
    else:
        print("❌ 配置更新失败")

def setup_full_features():
    """设置完整功能"""
    print("\n🚀 完整功能配置")
    print("需要OpenAI + Serper API密钥")
    
    openai_key = input("请输入OpenAI API密钥 (sk-开头): ").strip()
    if not openai_key:
        print("❌ 未输入OpenAI密钥")
        return
    
    serper_key = input("请输入Serper API密钥 (可选，回车跳过): ").strip()
    
    updates = {'OPENAI_API_KEY': openai_key}
    if serper_key:
        updates['SERPER_API_KEY'] = serper_key
    
    if update_env_file(updates):
        print("✅ 配置已更新")
        print("✅ 现在可以使用真实的AI分析和搜索功能")
    else:
        print("❌ 配置更新失败")

def setup_custom():
    """自定义配置"""
    print("\n🔧 自定义配置")
    print("可配置的选项:")
    print("1. OPENAI_API_KEY - OpenAI模型")
    print("2. ANTHROPIC_API_KEY - Claude模型")
    print("3. GOOGLE_API_KEY - Gemini模型")
    print("4. SERPER_API_KEY - Google搜索")
    print("5. TAVILY_API_KEY - AI优化搜索")
    
    updates = {}
    
    for key, desc in [
        ('OPENAI_API_KEY', 'OpenAI API密钥'),
        ('ANTHROPIC_API_KEY', 'Anthropic API密钥'),
        ('GOOGLE_API_KEY', 'Google API密钥'),
        ('SERPER_API_KEY', 'Serper API密钥'),
        ('TAVILY_API_KEY', 'Tavily API密钥')
    ]:
        value = input(f"{desc} (可选，回车跳过): ").strip()
        if value:
            updates[key] = value
    
    if updates:
        if update_env_file(updates):
            print("✅ 配置已更新")
        else:
            print("❌ 配置更新失败")
    else:
        print("ℹ️  未更新任何配置")

def show_current_config():
    """显示当前配置"""
    print("\n📋 当前配置状态:")
    
    if not os.path.exists('.env'):
        print("❌ 未找到 .env 文件")
        return
    
    # 检查关键配置
    configs = {}
    with open('.env', 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                configs[key.strip()] = value.strip()
    
    # 显示AI配置
    print("\n🤖 AI模型配置:")
    ai_keys = ['OPENAI_API_KEY', 'ANTHROPIC_API_KEY', 'GOOGLE_API_KEY']
    ai_configured = False
    for key in ai_keys:
        if key in configs and configs[key]:
            print(f"  ✅ {key}: 已配置")
            ai_configured = True
        else:
            print(f"  ❌ {key}: 未配置")
    
    if not ai_configured:
        print("  ℹ️  将使用演示模式")
    
    # 显示搜索配置
    print("\n🔍 搜索引擎配置:")
    search_keys = ['SERPER_API_KEY', 'TAVILY_API_KEY', 'BING_SEARCH_KEY']
    search_configured = False
    for key in search_keys:
        if key in configs and configs[key]:
            print(f"  ✅ {key}: 已配置")
            search_configured = True
        else:
            print(f"  ❌ {key}: 未配置")
    
    print("  ✅ DuckDuckGo: 自动可用 (无需配置)")
    
    if not search_configured:
        print("  ℹ️  将使用DuckDuckGo搜索")

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_env_file():
        return
    
    while True:
        choice = get_user_choice()
        
        if choice == 1:
            setup_demo_mode()
        elif choice == 2:
            setup_basic_ai()
        elif choice == 3:
            setup_full_features()
        elif choice == 4:
            setup_custom()
        elif choice == 5:
            show_current_config()
        
        print("\n" + "="*60)
        continue_choice = input("是否继续配置? (y/n): ").strip().lower()
        if continue_choice not in ['y', 'yes', '是']:
            break
    
    print("\n🎉 配置完成！")
    print("运行应用: python app.py")
    print("访问地址: http://localhost:7860")

if __name__ == "__main__":
    main()
