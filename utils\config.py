"""
Configuration management module for InsightPulse platform.
Handles environment variables, settings, and configuration validation.
"""

import os
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
from pydantic import Field, validator
from pydantic_settings import BaseSettings
import logging

# Load environment variables
load_dotenv()

class LLMConfig(BaseSettings):
    """LLM configuration settings"""
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    deepseek_api_key: Optional[str] = Field(default=None, env="DEEPSEEK_API_KEY")
    
    default_provider: str = Field(default="openai", env="DEFAULT_LLM_PROVIDER")
    default_model: str = Field(default="gpt-4o-mini", env="DEFAULT_MODEL")
    fallback_model: str = Field(default="gpt-3.5-turbo", env="FALLBACK_MODEL")
    
    max_tokens: int = Field(default=2048, env="MAX_TOKENS")
    temperature: float = Field(default=0.7, env="TEMPERATURE")
    timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    
    @validator('default_provider')
    def validate_provider(cls, v):
        valid_providers = ['openai', 'anthropic', 'deepseek']
        if v not in valid_providers:
            raise ValueError(f'Provider must be one of {valid_providers}')
        return v

class AppConfig(BaseSettings):
    """Application configuration settings"""
    app_name: str = Field(default="InsightPulse", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Internationalization
    default_language: str = Field(default="zh", env="DEFAULT_LANGUAGE")
    supported_languages: str = Field(default="zh,en", env="SUPPORTED_LANGUAGES")

    # API limits
    max_requests_per_minute: int = Field(default=60, env="MAX_REQUESTS_PER_MINUTE")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")

    # Cache settings
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    cache_ttl: int = Field(default=3600, env="CACHE_TTL")

    def get_supported_languages(self) -> List[str]:
        """Get supported languages as a list"""
        return [lang.strip() for lang in self.supported_languages.split(',')]

class ExternalAPIConfig(BaseSettings):
    """External API configuration"""
    github_token: Optional[str] = Field(default=None, env="GITHUB_TOKEN")
    producthunt_api_key: Optional[str] = Field(default=None, env="PRODUCTHUNT_API_KEY")
    
    # API endpoints
    github_api_base: str = "https://api.github.com"
    producthunt_api_base: str = "https://api.producthunt.com/v2"

class HuggingFaceConfig(BaseSettings):
    """Hugging Face Spaces configuration"""
    space_name: str = Field(default="explorer-agent", env="HF_SPACE_NAME")
    space_title: str = Field(default="逛逛 - 需求探索与可行性分析平台", env="HF_SPACE_TITLE")
    
    # Gradio settings
    gradio_theme: str = Field(default="gradio/soft", env="GRADIO_THEME")
    gradio_share: bool = Field(default=False, env="GRADIO_SHARE")
    gradio_server_port: int = Field(default=7860, env="GRADIO_SERVER_PORT")

class Config:
    """Main configuration class that combines all settings"""
    
    def __init__(self):
        self.llm = LLMConfig()
        self.app = AppConfig()
        self.external_api = ExternalAPIConfig()
        self.huggingface = HuggingFaceConfig()
        
        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, self.app.log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('app.log') if self.app.debug else logging.NullHandler()
            ]
        )
    
    def get_available_llm_providers(self) -> List[str]:
        """Get list of available LLM providers based on API keys"""
        providers = []
        if self.llm.openai_api_key:
            providers.append('openai')
        if self.llm.anthropic_api_key:
            providers.append('anthropic')
        if self.llm.deepseek_api_key:
            providers.append('deepseek')
        return providers
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate configuration and return status"""
        status = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check LLM API keys
        available_providers = self.get_available_llm_providers()
        if not available_providers:
            status['valid'] = False
            status['errors'].append("No LLM API keys configured")
        elif self.llm.default_provider not in available_providers:
            status['warnings'].append(f"Default provider '{self.llm.default_provider}' not available")
        
        # Check external APIs
        if not self.external_api.github_token:
            status['warnings'].append("GitHub token not configured - GitHub scanning will be limited")
        
        return status

# Global configuration instance
config = Config()

def get_config() -> Config:
    """Get the global configuration instance"""
    return config

def reload_config():
    """Reload configuration from environment"""
    global config
    load_dotenv(override=True)
    config = Config()
    return config
