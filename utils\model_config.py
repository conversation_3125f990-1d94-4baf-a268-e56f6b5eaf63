"""
Model configuration loader for InsightPulse platform.
Loads model configurations from JSON files for easy updates.
"""

import json
import os
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class ModelConfigLoader:
    """Loads and manages model configurations"""
    
    def __init__(self, config_path: str = "config/models.json"):
        self.config_path = Path(config_path)
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """Load configuration from JSON file"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                logger.info(f"Loaded model configuration from {self.config_path}")
            else:
                logger.warning(f"Config file not found: {self.config_path}")
                self.config_data = self._get_default_config()
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            self.config_data = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if file loading fails"""
        return {
            "model_providers": {
                "openai": {
                    "name": "OpenAI",
                    "models": ["gpt-4o-mini", "gpt-3.5-turbo"],
                    "env_key": "OPENAI_API_KEY",
                    "description": "OpenAI models",
                    "default_model": "gpt-4o-mini"
                }
            },
            "search_engines": {},
            "data_sources": {}
        }
    
    def get_model_providers(self) -> Dict[str, Any]:
        """Get all model providers"""
        return self.config_data.get("model_providers", {})
    
    def get_provider_info(self, provider_id: str) -> Optional[Dict[str, Any]]:
        """Get information for a specific provider"""
        return self.get_model_providers().get(provider_id)
    
    def get_provider_models(self, provider_id: str) -> List[str]:
        """Get models for a specific provider"""
        provider = self.get_provider_info(provider_id)
        return provider.get("models", []) if provider else []
    
    def get_default_model(self, provider_id: str) -> Optional[str]:
        """Get default model for a provider"""
        provider = self.get_provider_info(provider_id)
        if provider:
            return provider.get("default_model", provider.get("models", [None])[0])
        return None
    
    def get_available_providers(self) -> List[tuple]:
        """Get list of available providers based on environment variables"""
        available = []
        for provider_id, provider_info in self.get_model_providers().items():
            env_key = provider_info.get("env_key")
            if env_key and os.getenv(env_key):
                available.append((provider_info["name"], provider_id))
        
        # Always include demo mode
        available.append(("Demo Mode (No API Required)", "demo"))
        return available
    
    def get_search_engines(self) -> Dict[str, Any]:
        """Get search engine configurations"""
        return self.config_data.get("search_engines", {})
    
    def get_data_sources(self) -> Dict[str, Any]:
        """Get data source configurations"""
        return self.config_data.get("data_sources", {})
    
    def is_provider_available(self, provider_id: str) -> bool:
        """Check if a provider is available (has API key)"""
        if provider_id == "demo":
            return True
        
        provider = self.get_provider_info(provider_id)
        if not provider:
            return False
        
        env_key = provider.get("env_key")
        return bool(env_key and os.getenv(env_key))
    
    def get_provider_status(self, provider_id: str) -> Dict[str, Any]:
        """Get detailed status for a provider"""
        if provider_id == "demo":
            return {
                "available": True,
                "status": "✅",
                "message": "Demo mode - no API key required"
            }
        
        provider = self.get_provider_info(provider_id)
        if not provider:
            return {
                "available": False,
                "status": "❌",
                "message": "Provider not found"
            }
        
        env_key = provider.get("env_key")
        api_key = os.getenv(env_key) if env_key else None
        
        if api_key:
            return {
                "available": True,
                "status": "✅",
                "message": f"API key configured for {env_key}"
            }
        else:
            return {
                "available": False,
                "status": "❌",
                "message": f"API key missing for {env_key}"
            }
    
    def reload_config(self):
        """Reload configuration from file"""
        self.load_config()
    
    def save_config(self, config_data: Dict[str, Any]):
        """Save configuration to file"""
        try:
            # Ensure config directory exists
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.config_data = config_data
            logger.info(f"Configuration saved to {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to save config: {e}")
            raise

# Global instance
_model_config = None

def get_model_config() -> ModelConfigLoader:
    """Get global model configuration instance"""
    global _model_config
    if _model_config is None:
        _model_config = ModelConfigLoader()
    return _model_config

def reload_model_config():
    """Reload model configuration"""
    global _model_config
    if _model_config:
        _model_config.reload_config()
    else:
        _model_config = ModelConfigLoader()

# Convenience functions
def get_available_providers() -> List[tuple]:
    """Get available providers"""
    return get_model_config().get_available_providers()

def get_provider_models(provider_id: str) -> List[str]:
    """Get models for provider"""
    if provider_id == "demo":
        return ["demo-analyzer"]
    return get_model_config().get_provider_models(provider_id)

def get_provider_info(provider_id: str) -> Optional[Dict[str, Any]]:
    """Get provider information"""
    return get_model_config().get_provider_info(provider_id)

def get_provider_status(provider_id: str) -> Dict[str, Any]:
    """Get provider status"""
    return get_model_config().get_provider_status(provider_id)
