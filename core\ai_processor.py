"""
AI Processing Chain for InsightPulse platform.
Handles the complete AI analysis workflow from demand parsing to recommendations.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
import asyncio
from typing import Dict, List, Optional, Any, Tu<PERSON>
from dataclasses import dataclass, asdict
from enum import Enum

from llm.base import LLMManager, LLMMessage, LLMConfig
from core.demand_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>, DemandAnalysis, UserMode, DemandCategory
from utils.logger import get_logger
from utils.retry import llm_retry

logger = get_logger("AIProcessor")

@dataclass
class AnalysisRequest:
    """Request for AI analysis"""
    user_input: str
    language: str
    mode: str
    provider: str
    model: str
    user_id: Optional[str] = None

@dataclass
class CompetitorInfo:
    """Information about a competitor/solution"""
    name: str
    description: str
    features: List[str]
    pros: List[str]
    cons: List[str]
    rating: float
    pricing: str
    website: str
    last_updated: str

@dataclass
class FeasibilityScore:
    """Feasibility assessment scores"""
    technical_feasibility: float
    market_demand: float
    competition_level: float
    legal_risk: float
    cost_control: float
    market_acceptance: float

@dataclass
class InnovationOpportunity:
    """Innovation opportunity analysis"""
    recommendation_type: str  # "abandon", "proceed", "high_risk", "optimize"
    confidence: float
    reasoning: str
    next_steps: List[str]
    risk_warnings: List[str]
    feasibility_scores: FeasibilityScore

@dataclass
class AnalysisResult:
    """Complete analysis result"""
    request: AnalysisRequest
    demand_analysis: DemandAnalysis
    competitors: List[CompetitorInfo]
    innovation_opportunity: Optional[InnovationOpportunity]
    processing_time: float
    success: bool
    error_message: Optional[str] = None

class AIProcessor:
    """Main AI processing engine"""
    
    def __init__(self, llm_manager: LLMManager):
        self.llm_manager = llm_manager
        self.demand_parser = DemandParser(llm_manager)
        
        # Processing chain configuration
        self.chain_config = {
            "demand_analysis": True,
            "competitor_research": True,
            "feasibility_assessment": True,
            "innovation_analysis": True,
            "recommendation_generation": True
        }
    
    async def process_request(self, request: AnalysisRequest) -> AnalysisResult:
        """
        Process a complete analysis request through the AI chain
        
        Args:
            request: Analysis request containing user input and preferences
            
        Returns:
            AnalysisResult: Complete analysis result
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            logger.info(f"Starting AI processing chain for request: {request.user_input[:50]}...")
            
            # Step 1: Demand Analysis
            demand_analysis = await self._analyze_demand(request)
            logger.info(f"Demand analysis completed: {demand_analysis.user_mode.value} mode, {demand_analysis.category.value}")
            
            # Step 2: Competitor Research (for user mode)
            competitors = []
            if demand_analysis.user_mode == UserMode.USER:
                competitors = await self._research_competitors(request, demand_analysis)
                logger.info(f"Found {len(competitors)} competitors")
            
            # Step 3: Innovation Opportunity Analysis (for developer mode)
            innovation_opportunity = None
            if demand_analysis.user_mode == UserMode.DEVELOPER:
                innovation_opportunity = await self._analyze_innovation_opportunity(request, demand_analysis)
                logger.info(f"Innovation analysis completed: {innovation_opportunity.recommendation_type}")
            
            processing_time = asyncio.get_event_loop().time() - start_time
            
            result = AnalysisResult(
                request=request,
                demand_analysis=demand_analysis,
                competitors=competitors,
                innovation_opportunity=innovation_opportunity,
                processing_time=processing_time,
                success=True
            )
            
            logger.info(f"AI processing completed successfully in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"AI processing failed: {e}")
            
            return AnalysisResult(
                request=request,
                demand_analysis=self._create_fallback_demand_analysis(request),
                competitors=[],
                innovation_opportunity=None,
                processing_time=processing_time,
                success=False,
                error_message=str(e)
            )
    
    async def _analyze_demand(self, request: AnalysisRequest) -> DemandAnalysis:
        """Step 1: Analyze user demand using LLM"""
        
        # Use the existing demand parser with async support
        loop = asyncio.get_event_loop()
        demand_analysis = await loop.run_in_executor(
            None, 
            self.demand_parser.parse_demand, 
            request.user_input, 
            request.language
        )
        
        return demand_analysis
    
    async def _research_competitors(self, request: AnalysisRequest, demand_analysis: DemandAnalysis) -> List[CompetitorInfo]:
        """Step 2: Research competitors and existing solutions"""
        
        try:
            # Prepare LLM prompt for competitor research
            system_prompt = self._get_competitor_research_prompt(request.language)
            user_prompt = self._format_competitor_research_query(demand_analysis, request.language)
            
            messages = [
                LLMMessage(role="system", content=system_prompt),
                LLMMessage(role="user", content=user_prompt)
            ]
            
            config = LLMConfig(
                model=request.model,
                temperature=0.3,
                max_tokens=2000,
                language=request.language
            )
            
            response = self.llm_manager.generate_with_fallback(messages, config, request.provider)
            
            if response.success:
                competitors = self._parse_competitor_response(response.content, request.language)
                return competitors
            else:
                logger.warning(f"Competitor research failed: {response.error}")
                return self._get_fallback_competitors(demand_analysis.category, request.language)
                
        except Exception as e:
            logger.error(f"Competitor research error: {e}")
            return self._get_fallback_competitors(demand_analysis.category, request.language)
    
    async def _analyze_innovation_opportunity(self, request: AnalysisRequest, demand_analysis: DemandAnalysis) -> InnovationOpportunity:
        """Step 3: Analyze innovation opportunities for developer mode"""
        
        try:
            # Prepare LLM prompt for innovation analysis
            system_prompt = self._get_innovation_analysis_prompt(request.language)
            user_prompt = self._format_innovation_analysis_query(demand_analysis, request.language)
            
            messages = [
                LLMMessage(role="system", content=system_prompt),
                LLMMessage(role="user", content=user_prompt)
            ]
            
            config = LLMConfig(
                model=request.model,
                temperature=0.4,
                max_tokens=2000,
                language=request.language
            )
            
            response = self.llm_manager.generate_with_fallback(messages, config, request.provider)
            
            if response.success:
                innovation_opportunity = self._parse_innovation_response(response.content, request.language)
                return innovation_opportunity
            else:
                logger.warning(f"Innovation analysis failed: {response.error}")
                return self._get_fallback_innovation_opportunity(demand_analysis, request.language)
                
        except Exception as e:
            logger.error(f"Innovation analysis error: {e}")
            return self._get_fallback_innovation_opportunity(demand_analysis, request.language)
    
    def _get_competitor_research_prompt(self, language: str) -> str:
        """Get system prompt for competitor research"""
        if language == "zh":
            return """
你是一个专业的市场研究分析师。你的任务是根据用户需求，研究现有的解决方案和竞品。

请按照以下JSON格式返回结果：
{
    "competitors": [
        {
            "name": "产品名称",
            "description": "简要描述",
            "features": ["功能1", "功能2", "功能3"],
            "pros": ["优势1", "优势2"],
            "cons": ["劣势1", "劣势2"],
            "rating": 4.5,
            "pricing": "定价信息",
            "website": "官网地址",
            "last_updated": "最近更新时间"
        }
    ]
}

要求：
1. 提供3-5个最相关的解决方案
2. 信息要准确和最新
3. 包含不同类型的解决方案（免费/付费、开源/商业等）
4. 评分基于用户评价和市场表现
"""
        else:
            return """
You are a professional market research analyst. Your task is to research existing solutions and competitors based on user requirements.

Please return results in the following JSON format:
{
    "competitors": [
        {
            "name": "Product Name",
            "description": "Brief description",
            "features": ["Feature 1", "Feature 2", "Feature 3"],
            "pros": ["Advantage 1", "Advantage 2"],
            "cons": ["Disadvantage 1", "Disadvantage 2"],
            "rating": 4.5,
            "pricing": "Pricing information",
            "website": "Official website",
            "last_updated": "Last update time"
        }
    ]
}

Requirements:
1. Provide 3-5 most relevant solutions
2. Information should be accurate and up-to-date
3. Include different types of solutions (free/paid, open source/commercial, etc.)
4. Rating based on user reviews and market performance
"""
    
    def _format_competitor_research_query(self, demand_analysis: DemandAnalysis, language: str) -> str:
        """Format the competitor research query"""
        if language == "zh":
            return f"""
用户需求分析：
- 用户意图：{demand_analysis.user_intent}
- 需求领域：{demand_analysis.category.value}
- 关键词：{', '.join(demand_analysis.keywords)}
- 约束条件：{', '.join(demand_analysis.constraints)}

请研究满足这个需求的现有解决方案和竞品，重点关注：
1. 功能匹配度
2. 用户评价
3. 市场占有率
4. 定价策略
5. 最新发展动态

请提供详细的竞品分析报告。
"""
        else:
            return f"""
User Requirement Analysis:
- User Intent: {demand_analysis.user_intent}
- Domain: {demand_analysis.category.value}
- Keywords: {', '.join(demand_analysis.keywords)}
- Constraints: {', '.join(demand_analysis.constraints)}

Please research existing solutions and competitors that meet this requirement, focusing on:
1. Feature matching
2. User reviews
3. Market share
4. Pricing strategy
5. Latest developments

Please provide a detailed competitive analysis report.
"""
    
    def _get_innovation_analysis_prompt(self, language: str) -> str:
        """Get system prompt for innovation analysis"""
        if language == "zh":
            return """
你是一个专业的创新分析师和商业顾问。你的任务是评估创新项目的可行性和市场机会。

请按照以下JSON格式返回结果：
{
    "recommendation_type": "proceed|abandon|high_risk|optimize",
    "confidence": 0.85,
    "reasoning": "详细的分析理由",
    "next_steps": ["下一步行动1", "下一步行动2"],
    "risk_warnings": ["风险提醒1", "风险提醒2"],
    "feasibility_scores": {
        "technical_feasibility": 0.85,
        "market_demand": 0.78,
        "competition_level": 0.65,
        "legal_risk": 0.90,
        "cost_control": 0.70,
        "market_acceptance": 0.82
    }
}

评估维度说明：
- technical_feasibility: 技术可行性 (0-1)
- market_demand: 市场需求强度 (0-1)
- competition_level: 竞争饱和度 (0-1, 越低越好)
- legal_risk: 法律合规风险 (0-1, 越高越安全)
- cost_control: 成本控制能力 (0-1)
- market_acceptance: 市场接受度 (0-1)

推荐类型：
- proceed: 建议推进
- abandon: 建议放弃
- high_risk: 高风险警告
- optimize: 需要优化
"""
        else:
            return """
You are a professional innovation analyst and business consultant. Your task is to assess the feasibility and market opportunities of innovation projects.

Please return results in the following JSON format:
{
    "recommendation_type": "proceed|abandon|high_risk|optimize",
    "confidence": 0.85,
    "reasoning": "Detailed analysis reasoning",
    "next_steps": ["Next action 1", "Next action 2"],
    "risk_warnings": ["Risk warning 1", "Risk warning 2"],
    "feasibility_scores": {
        "technical_feasibility": 0.85,
        "market_demand": 0.78,
        "competition_level": 0.65,
        "legal_risk": 0.90,
        "cost_control": 0.70,
        "market_acceptance": 0.82
    }
}

Assessment dimensions:
- technical_feasibility: Technical feasibility (0-1)
- market_demand: Market demand intensity (0-1)
- competition_level: Competition saturation (0-1, lower is better)
- legal_risk: Legal compliance risk (0-1, higher is safer)
- cost_control: Cost control capability (0-1)
- market_acceptance: Market acceptance (0-1)

Recommendation types:
- proceed: Recommend proceeding
- abandon: Recommend abandoning
- high_risk: High risk warning
- optimize: Needs optimization
"""
    
    def _format_innovation_analysis_query(self, demand_analysis: DemandAnalysis, language: str) -> str:
        """Format the innovation analysis query"""
        if language == "zh":
            return f"""
创新项目分析：
- 项目概念：{demand_analysis.user_intent}
- 技术领域：{demand_analysis.category.value}
- 核心关键词：{', '.join(demand_analysis.keywords)}
- 项目约束：{', '.join(demand_analysis.constraints)}

请从以下角度进行全面的可行性分析：
1. 技术实现难度和可行性
2. 市场需求验证和规模评估
3. 竞争环境分析和差异化机会
4. 法律法规风险评估
5. 资源投入和成本控制
6. 用户接受度和推广难度
7. 商业模式可持续性

请提供明确的行动建议和风险提醒。
"""
        else:
            return f"""
Innovation Project Analysis:
- Project Concept: {demand_analysis.user_intent}
- Technical Domain: {demand_analysis.category.value}
- Core Keywords: {', '.join(demand_analysis.keywords)}
- Project Constraints: {', '.join(demand_analysis.constraints)}

Please conduct a comprehensive feasibility analysis from the following perspectives:
1. Technical implementation difficulty and feasibility
2. Market demand validation and scale assessment
3. Competitive environment analysis and differentiation opportunities
4. Legal and regulatory risk assessment
5. Resource investment and cost control
6. User acceptance and promotion difficulty
7. Business model sustainability

Please provide clear action recommendations and risk warnings.
"""

    def _parse_competitor_response(self, response_content: str, language: str) -> List[CompetitorInfo]:
        """Parse LLM response for competitor information"""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                competitors = []

                for comp_data in data.get("competitors", []):
                    competitor = CompetitorInfo(
                        name=comp_data.get("name", "Unknown"),
                        description=comp_data.get("description", ""),
                        features=comp_data.get("features", []),
                        pros=comp_data.get("pros", []),
                        cons=comp_data.get("cons", []),
                        rating=float(comp_data.get("rating", 0)),
                        pricing=comp_data.get("pricing", "Unknown"),
                        website=comp_data.get("website", ""),
                        last_updated=comp_data.get("last_updated", "Unknown")
                    )
                    competitors.append(competitor)

                return competitors
            else:
                logger.warning("No JSON found in competitor response")
                return []

        except Exception as e:
            logger.error(f"Failed to parse competitor response: {e}")
            return []

    def _parse_innovation_response(self, response_content: str, language: str) -> InnovationOpportunity:
        """Parse LLM response for innovation opportunity"""
        try:
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())

                feasibility_data = data.get("feasibility_scores", {})
                feasibility_scores = FeasibilityScore(
                    technical_feasibility=float(feasibility_data.get("technical_feasibility", 0.5)),
                    market_demand=float(feasibility_data.get("market_demand", 0.5)),
                    competition_level=float(feasibility_data.get("competition_level", 0.5)),
                    legal_risk=float(feasibility_data.get("legal_risk", 0.5)),
                    cost_control=float(feasibility_data.get("cost_control", 0.5)),
                    market_acceptance=float(feasibility_data.get("market_acceptance", 0.5))
                )

                innovation_opportunity = InnovationOpportunity(
                    recommendation_type=data.get("recommendation_type", "optimize"),
                    confidence=float(data.get("confidence", 0.5)),
                    reasoning=data.get("reasoning", ""),
                    next_steps=data.get("next_steps", []),
                    risk_warnings=data.get("risk_warnings", []),
                    feasibility_scores=feasibility_scores
                )

                return innovation_opportunity
            else:
                logger.warning("No JSON found in innovation response")
                return self._get_default_innovation_opportunity(language)

        except Exception as e:
            logger.error(f"Failed to parse innovation response: {e}")
            return self._get_default_innovation_opportunity(language)

    def _get_fallback_competitors(self, category: DemandCategory, language: str) -> List[CompetitorInfo]:
        """Get fallback competitors when LLM fails"""
        # Import demo data
        from app_demo import DEMO_SOLUTIONS

        solutions = DEMO_SOLUTIONS.get(category, {}).get(language, [])
        competitors = []

        for solution in solutions:
            competitor = CompetitorInfo(
                name=solution["name"],
                description=solution.get("description", ""),
                features=solution["features"].split("、") if language == "zh" else solution["features"].split(", "),
                pros=[solution["pros"]],
                cons=[solution["cons"]],
                rating=float(solution["rating"].split("/")[0]),
                pricing="See website",
                website=solution["website"],
                last_updated="2024"
            )
            competitors.append(competitor)

        return competitors

    def _get_fallback_innovation_opportunity(self, demand_analysis: DemandAnalysis, language: str) -> InnovationOpportunity:
        """Get fallback innovation opportunity when LLM fails"""

        feasibility_scores = FeasibilityScore(
            technical_feasibility=0.75,
            market_demand=0.70,
            competition_level=0.60,
            legal_risk=0.85,
            cost_control=0.65,
            market_acceptance=0.70
        )

        if language == "zh":
            reasoning = "基于关键词分析，该项目具有一定的技术可行性和市场需求。建议进行更详细的市场调研。"
            next_steps = ["进行市场调研", "制作原型", "寻找目标用户"]
            risk_warnings = ["注意技术风险", "关注竞争动态"]
        else:
            reasoning = "Based on keyword analysis, this project has certain technical feasibility and market demand. Recommend conducting more detailed market research."
            next_steps = ["Conduct market research", "Create prototype", "Find target users"]
            risk_warnings = ["Pay attention to technical risks", "Monitor competitive dynamics"]

        return InnovationOpportunity(
            recommendation_type="optimize",
            confidence=0.6,
            reasoning=reasoning,
            next_steps=next_steps,
            risk_warnings=risk_warnings,
            feasibility_scores=feasibility_scores
        )

    def _get_default_innovation_opportunity(self, language: str) -> InnovationOpportunity:
        """Get default innovation opportunity"""
        return self._get_fallback_innovation_opportunity(None, language)

    def _create_fallback_demand_analysis(self, request: AnalysisRequest) -> DemandAnalysis:
        """Create fallback demand analysis when processing fails"""
        from core.demand_parser import DemandAnalysis, UserMode, DemandCategory

        return DemandAnalysis(
            user_intent=request.user_input,
            category=DemandCategory.OTHER,
            keywords=[],
            constraints=[],
            user_mode=UserMode.USER,
            confidence_score=0.3,
            language=request.language,
            raw_input=request.user_input,
            metadata={"fallback": True, "error": "Processing failed"}
        )

# Factory function to create AI processor
def create_ai_processor(llm_manager: LLMManager) -> AIProcessor:
    """Factory function to create AI processor"""
    return AIProcessor(llm_manager)

# Utility functions for result formatting
def format_analysis_result_for_ui(result: AnalysisResult, language: str) -> Tuple[str, str, str]:
    """Format analysis result for UI display"""

    if not result.success:
        error_msg = "分析过程中出现错误" if language == "zh" else "Error occurred during analysis"
        return error_msg, "", ""

    # Format demand analysis
    analysis_text = _format_demand_analysis_ui(result.demand_analysis, result.request, language)

    # Format recommendations
    if result.demand_analysis.user_mode == UserMode.USER and result.competitors:
        recommendations_text = _format_competitors_ui(result.competitors, language)
    elif result.innovation_opportunity:
        recommendations_text = _format_innovation_opportunity_ui(result.innovation_opportunity, language)
    else:
        recommendations_text = "No recommendations available" if language == "en" else "暂无推荐内容"

    # Format visualization
    visualization_text = _format_visualization_ui(result, language)

    return analysis_text, recommendations_text, visualization_text

def _format_demand_analysis_ui(analysis: DemandAnalysis, request: AnalysisRequest, language: str) -> str:
    """Format demand analysis for UI"""

    if language == "zh":
        mode_text = "开发者模式" if analysis.user_mode == UserMode.DEVELOPER else "普通用户模式"
        category_text = {
            DemandCategory.PRODUCTIVITY: "效率工具",
            DemandCategory.DEVELOPMENT: "开发工具",
            DemandCategory.EDUCATION: "教育",
            DemandCategory.ENTERTAINMENT: "娱乐",
            DemandCategory.OTHER: "其他"
        }.get(analysis.category, "其他")

        provider_info = f"**AI 分析引擎**: {request.provider} - {request.model}"

        return f"""
## 📋 需求分析结果

{provider_info}

**用户意图**: {analysis.user_intent}

**需求领域**: {category_text}

**关键词**: {', '.join(analysis.keywords)}

**用户模式**: {mode_text}

**分析置信度**: {analysis.confidence_score:.1%}

**处理时间**: {getattr(analysis, 'processing_time', 0):.2f}秒

---
*AI深度分析完成*
"""
    else:
        mode_text = "Developer Mode" if analysis.user_mode == UserMode.DEVELOPER else "User Mode"
        category_text = {
            DemandCategory.PRODUCTIVITY: "Productivity Tools",
            DemandCategory.DEVELOPMENT: "Development Tools",
            DemandCategory.EDUCATION: "Education",
            DemandCategory.ENTERTAINMENT: "Entertainment",
            DemandCategory.OTHER: "Other"
        }.get(analysis.category, "Other")

        provider_info = f"**AI Analysis Engine**: {request.provider} - {request.model}"

        return f"""
## 📋 Analysis Results

{provider_info}

**User Intent**: {analysis.user_intent}

**Domain Category**: {category_text}

**Keywords**: {', '.join(analysis.keywords)}

**User Mode**: {mode_text}

**Confidence Score**: {analysis.confidence_score:.1%}

**Processing Time**: {getattr(analysis, 'processing_time', 0):.2f}s

---
*AI Deep Analysis Completed*
"""

def _format_competitors_ui(competitors: List[CompetitorInfo], language: str) -> str:
    """Format competitors for UI"""

    if language == "zh":
        result = "## 🔍 AI推荐解决方案\n\n"
        for i, comp in enumerate(competitors, 1):
            result += f"""
### {i}. {comp.name}
- **评分**: {comp.rating}/5
- **核心功能**: {', '.join(comp.features[:3])}
- **优势**: {', '.join(comp.pros)}
- **劣势**: {', '.join(comp.cons)}
- **定价**: {comp.pricing}
- **官网**: [{comp.name}]({comp.website})

"""
        result += "\n*基于AI分析的个性化推荐*"
    else:
        result = "## 🔍 AI-Recommended Solutions\n\n"
        for i, comp in enumerate(competitors, 1):
            result += f"""
### {i}. {comp.name}
- **Rating**: {comp.rating}/5
- **Core Features**: {', '.join(comp.features[:3])}
- **Pros**: {', '.join(comp.pros)}
- **Cons**: {', '.join(comp.cons)}
- **Pricing**: {comp.pricing}
- **Website**: [{comp.name}]({comp.website})

"""
        result += "\n*Personalized recommendations based on AI analysis*"

    return result

def _format_innovation_opportunity_ui(opportunity: InnovationOpportunity, language: str) -> str:
    """Format innovation opportunity for UI"""

    recommendation_icons = {
        "proceed": "💎",
        "abandon": "🚫",
        "high_risk": "⚠️",
        "optimize": "🔧"
    }

    icon = recommendation_icons.get(opportunity.recommendation_type, "💡")

    if language == "zh":
        recommendation_texts = {
            "proceed": "建议推进",
            "abandon": "建议放弃",
            "high_risk": "高风险警告",
            "optimize": "需要优化"
        }

        rec_text = recommendation_texts.get(opportunity.recommendation_type, "需要评估")

        result = f"""
## 💡 AI创新机会分析

### 🎯 推荐行动
{icon} **{rec_text}** (置信度: {opportunity.confidence:.1%})

### 📊 AI可行性评估
- **技术可行性**: {opportunity.feasibility_scores.technical_feasibility:.1%}
- **市场需求**: {opportunity.feasibility_scores.market_demand:.1%}
- **竞争程度**: {opportunity.feasibility_scores.competition_level:.1%}
- **法律风险**: {opportunity.feasibility_scores.legal_risk:.1%}
- **成本控制**: {opportunity.feasibility_scores.cost_control:.1%}
- **市场接受度**: {opportunity.feasibility_scores.market_acceptance:.1%}

### 🧠 AI分析理由
{opportunity.reasoning}

### 🚀 建议下一步
{chr(10).join([f"- {step}" for step in opportunity.next_steps])}

### ⚠️ 风险提醒
{chr(10).join([f"- {warning}" for warning in opportunity.risk_warnings])}

*基于AI深度分析的专业建议*
"""
    else:
        recommendation_texts = {
            "proceed": "Recommend Proceeding",
            "abandon": "Recommend Abandoning",
            "high_risk": "High Risk Warning",
            "optimize": "Needs Optimization"
        }

        rec_text = recommendation_texts.get(opportunity.recommendation_type, "Needs Assessment")

        result = f"""
## 💡 AI Innovation Opportunity Analysis

### 🎯 Recommended Action
{icon} **{rec_text}** (Confidence: {opportunity.confidence:.1%})

### 📊 AI Feasibility Assessment
- **Technical Feasibility**: {opportunity.feasibility_scores.technical_feasibility:.1%}
- **Market Demand**: {opportunity.feasibility_scores.market_demand:.1%}
- **Competition Level**: {opportunity.feasibility_scores.competition_level:.1%}
- **Legal Risk**: {opportunity.feasibility_scores.legal_risk:.1%}
- **Cost Control**: {opportunity.feasibility_scores.cost_control:.1%}
- **Market Acceptance**: {opportunity.feasibility_scores.market_acceptance:.1%}

### 🧠 AI Analysis Reasoning
{opportunity.reasoning}

### 🚀 Recommended Next Steps
{chr(10).join([f"- {step}" for step in opportunity.next_steps])}

### ⚠️ Risk Warnings
{chr(10).join([f"- {warning}" for warning in opportunity.risk_warnings])}

*Professional recommendations based on AI deep analysis*
"""

    return result

def _format_visualization_ui(result: AnalysisResult, language: str) -> str:
    """Format visualization for UI"""

    if language == "zh":
        return f"""
## 📊 AI数据可视化

### ⏱️ 处理性能
- **总处理时间**: {result.processing_time:.2f}秒
- **AI模型**: {result.request.provider} - {result.request.model}
- **分析深度**: {"深度AI分析" if result.success else "基础分析"}

### 📈 分析统计
- **需求复杂度**: {len(result.demand_analysis.keywords)} 个关键词
- **置信度**: {result.demand_analysis.confidence_score:.1%}
- **推荐数量**: {len(result.competitors) if result.competitors else 0} 个解决方案

*实时AI分析数据*
"""
    else:
        return f"""
## 📊 AI Data Visualization

### ⏱️ Processing Performance
- **Total Processing Time**: {result.processing_time:.2f}s
- **AI Model**: {result.request.provider} - {result.request.model}
- **Analysis Depth**: {"Deep AI Analysis" if result.success else "Basic Analysis"}

### 📈 Analysis Statistics
- **Requirement Complexity**: {len(result.demand_analysis.keywords)} keywords
- **Confidence**: {result.demand_analysis.confidence_score:.1%}
- **Recommendations**: {len(result.competitors) if result.competitors else 0} solutions

*Real-time AI analysis data*
"""
