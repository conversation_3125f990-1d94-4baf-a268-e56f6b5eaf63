# ========================================
# 🔑 InsightPulse 环境变量配置
# ========================================
#
# 🚀 快速开始 (3选1):
# 1. 不填任何内容 → 演示模式 (可以测试所有功能)
# 2. 只填 OPENAI_API_KEY → 真实AI分析
# 3. 再加 SERPER_API_KEY → 完整功能 (AI + 搜索)
#
# 📋 使用方法:
# 1. 复制: cp .env.example .env
# 2. 编辑: 填入你的API密钥 (可选)
# 3. 启动: python app.py
# ========================================

# ========================================
# 🤖 AI模型 (推荐配置)
# ========================================

# 🔥 OpenAI (推荐新手) - 最简单的配置
# 获取地址: https://platform.openai.com/api-keys
OPENAI_API_KEY=

# 🔀 或者使用其他AI服务
# ANTHROPIC_API_KEY=        # Claude模型
# GOOGLE_API_KEY=           # Gemini模型
# DEEPSEEK_API_KEY=         # 中文优化模型

# ========================================
# � OpenAI兼容格式 (自定义API)
# ========================================

# 🏠 本地部署模型 (如 Ollama, LocalAI, vLLM等)
# 示例: http://localhost:11434/v1 (Ollama)
# 示例: http://localhost:8000/v1 (vLLM)
OPENAI_COMPATIBLE_API_BASE=
OPENAI_COMPATIBLE_API_KEY=sk-no-key-required
OPENAI_COMPATIBLE_MODEL=llama3.1:8b

# 🌐 第三方OpenAI兼容服务
# 示例: https://api.deepseek.com/v1
# 示例: https://api.moonshot.cn/v1
# 示例: https://api.zhipuai.cn/api/paas/v4
CUSTOM_OPENAI_API_BASE=
CUSTOM_OPENAI_API_KEY=
CUSTOM_OPENAI_MODEL=

# 🚀 一键配置常用服务
# 月之暗面 Kimi
# CUSTOM_OPENAI_API_BASE=https://api.moonshot.cn/v1
# CUSTOM_OPENAI_API_KEY=your_moonshot_key
# CUSTOM_OPENAI_MODEL=moonshot-v1-8k

# 智谱AI GLM
# CUSTOM_OPENAI_API_BASE=https://open.bigmodel.cn/api/paas/v4
# CUSTOM_OPENAI_API_KEY=your_zhipu_key
# CUSTOM_OPENAI_MODEL=glm-4

# 硅基流动
# CUSTOM_OPENAI_API_BASE=https://api.siliconflow.cn/v1
# CUSTOM_OPENAI_API_KEY=your_siliconflow_key
# CUSTOM_OPENAI_MODEL=Qwen/Qwen2.5-7B-Instruct

# ========================================
# �🔍 搜索引擎 (可选 - 增强搜索功能)
# ========================================

# 🔥 Serper Google Search (推荐) - Google搜索API
# 获取地址: https://serper.dev/
# 免费额度: 2500次查询/月
# 功能: 实时Google搜索结果
SERPER_API_KEY=

# ⚡ Tavily Search (推荐) - AI优化搜索
# 获取地址: https://tavily.com/
# 免费额度: 1000次查询/月
# 功能: AI优化的搜索结果，支持实时网页内容
TAVILY_API_KEY=

# 🌐 Google Custom Search - 官方Google搜索API
# 获取地址: https://developers.google.com/custom-search/v1/introduction
# 需要: API Key + Search Engine ID
# 功能: 官方Google搜索API
GOOGLE_SEARCH_API_KEY=
GOOGLE_SEARCH_ENGINE_ID=

# 🔵 Bing Search - 微软搜索API
# 获取地址: https://www.microsoft.com/en-us/bing/apis/bing-web-search-api
# 免费额度: 3000次查询/月
# 功能: Bing搜索结果
BING_SEARCH_KEY=

# 🇨🇳 百度搜索 - 百度搜索API
# 获取地址: https://ai.baidu.com/tech/websearch
# 功能: 百度搜索结果，适合中文内容
BAIDU_SEARCH_API_KEY=
BAIDU_SEARCH_SECRET_KEY=

# 🔍 DuckDuckGo - 隐私友好搜索
# 无需API密钥，但有频率限制
# 功能: 隐私保护的搜索结果
# 注意: 无需配置，自动可用

# 📊 GitHub - 开源项目搜索
# 获取地址: https://github.com/settings/tokens
# 权限: public_repo (只读)
# 功能: 搜索GitHub仓库和项目
GITHUB_TOKEN=

# 🚀 Product Hunt - 产品发现
# 获取地址: https://api.producthunt.com/v2/oauth/applications
# 功能: 搜索产品和创业项目
PRODUCTHUNT_API_KEY=

# 💼 Crunchbase - 企业和投资信息
# 获取地址: https://data.crunchbase.com/docs
# 功能: 企业信息、投资数据、竞品分析
CRUNCHBASE_API_KEY=

# ========================================
# 📋 快速配置示例
# ========================================

# 🚀 方案1: 官方OpenAI (新手推荐)
# OPENAI_API_KEY=sk-your_openai_key_here

# 🏠 方案2: 本地模型 (Ollama/LocalAI)
# OPENAI_COMPATIBLE_API_BASE=http://localhost:11434/v1
# OPENAI_COMPATIBLE_API_KEY=sk-no-key-required
# OPENAI_COMPATIBLE_MODEL=llama3.1:8b

# 🌐 方案3: 第三方兼容服务 (如月之暗面Kimi)
# CUSTOM_OPENAI_API_BASE=https://api.moonshot.cn/v1
# CUSTOM_OPENAI_API_KEY=your_moonshot_key
# CUSTOM_OPENAI_MODEL=moonshot-v1-8k

# 🔥 方案4: 官方API + 搜索功能
# OPENAI_API_KEY=sk-your_openai_key_here
# SERPER_API_KEY=your_serper_key_here

# 💪 方案5: 完整配置 - 多个AI提供商
# OPENAI_API_KEY=sk-your_openai_key_here
# ANTHROPIC_API_KEY=sk-ant-your_anthropic_key_here
# SERPER_API_KEY=your_serper_key_here
# GITHUB_TOKEN=ghp_your_github_token_here

# ========================================
# ⚠️  重要提醒
# ========================================
# 1. 不填任何密钥也可以运行 (演示模式)
# 2. 只填一个OpenAI密钥就能获得完整AI功能
# 3. 添加搜索密钥可以获得实时搜索功能
# 4. 所有密钥都是可选的，按需添加
# 5. 不要将.env文件提交到Git仓库
# ========================================
