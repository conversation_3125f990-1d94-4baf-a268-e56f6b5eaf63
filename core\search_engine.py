"""
Search Engine for InsightPulse platform.
Implements comprehensive search across multiple data sources for competitor analysis.
Based on DeepSearch methodology and multi-source aggregation.
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
import re
from urllib.parse import quote_plus, urljoin

from utils.model_config import get_model_config
from utils.logger import get_logger
from utils.retry import async_retry

logger = get_logger("SearchEngine")

@dataclass
class SearchResult:
    """Single search result"""
    title: str
    url: str
    snippet: str
    source: str
    score: float = 0.0
    metadata: Optional[Dict[str, Any]] = None
    timestamp: Optional[str] = None

@dataclass
class SearchQuery:
    """Search query configuration"""
    query: str
    language: str = "en"
    max_results: int = 10
    sources: List[str] = None  # ["web", "github", "producthunt", "crunchbase"]
    filters: Optional[Dict[str, Any]] = None
    time_range: Optional[str] = None  # "day", "week", "month", "year"

@dataclass
class SearchResponse:
    """Aggregated search response"""
    query: SearchQuery
    results: List[SearchResult]
    total_results: int
    search_time: float
    sources_used: List[str]
    success: bool = True
    error_message: Optional[str] = None

class BaseSearchProvider:
    """Base class for search providers"""
    
    def __init__(self, api_key: str, base_url: str):
        self.api_key = api_key
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    @async_retry(max_attempts=3, base_delay=1.0)
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Implement in subclasses"""
        raise NotImplementedError

class SerperSearchProvider(BaseSearchProvider):
    """Serper Google Search API provider"""
    
    def __init__(self, api_key: str):
        super().__init__(api_key, "https://google.serper.dev")
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search using Serper API"""
        try:
            headers = {
                "X-API-KEY": self.api_key,
                "Content-Type": "application/json"
            }
            
            payload = {
                "q": query.query,
                "num": min(query.max_results, 100),
                "hl": query.language,
                "gl": "us" if query.language == "en" else "cn"
            }
            
            # Add time range filter
            if query.time_range:
                time_filters = {
                    "day": "qdr:d",
                    "week": "qdr:w", 
                    "month": "qdr:m",
                    "year": "qdr:y"
                }
                if query.time_range in time_filters:
                    payload["tbs"] = time_filters[query.time_range]
            
            async with self.session.post(
                f"{self.base_url}/search",
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_serper_results(data, "web")
                else:
                    logger.error(f"Serper API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Serper search error: {e}")
            return []
    
    def _parse_serper_results(self, data: Dict, source: str) -> List[SearchResult]:
        """Parse Serper API response"""
        results = []
        
        # Parse organic results
        for item in data.get("organic", []):
            result = SearchResult(
                title=item.get("title", ""),
                url=item.get("link", ""),
                snippet=item.get("snippet", ""),
                source=source,
                score=item.get("position", 0),
                metadata={
                    "position": item.get("position"),
                    "date": item.get("date")
                }
            )
            results.append(result)
        
        # Parse knowledge graph if available
        if "knowledgeGraph" in data:
            kg = data["knowledgeGraph"]
            result = SearchResult(
                title=kg.get("title", ""),
                url=kg.get("website", ""),
                snippet=kg.get("description", ""),
                source="knowledge_graph",
                score=0,  # Highest priority
                metadata={
                    "type": kg.get("type"),
                    "attributes": kg.get("attributes", {})
                }
            )
            results.insert(0, result)
        
        return results

class GitHubSearchProvider(BaseSearchProvider):
    """GitHub API search provider"""
    
    def __init__(self, api_key: str):
        super().__init__(api_key, "https://api.github.com")
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search GitHub repositories"""
        try:
            headers = {
                "Authorization": f"token {self.api_key}",
                "Accept": "application/vnd.github.v3+json"
            }
            
            # Search repositories
            search_query = f"{query.query} in:name,description,readme"
            params = {
                "q": search_query,
                "sort": "stars",
                "order": "desc",
                "per_page": min(query.max_results, 100)
            }
            
            async with self.session.get(
                f"{self.base_url}/search/repositories",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_github_results(data)
                else:
                    logger.error(f"GitHub API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"GitHub search error: {e}")
            return []
    
    def _parse_github_results(self, data: Dict) -> List[SearchResult]:
        """Parse GitHub API response"""
        results = []
        
        for item in data.get("items", []):
            result = SearchResult(
                title=item.get("full_name", ""),
                url=item.get("html_url", ""),
                snippet=item.get("description", ""),
                source="github",
                score=item.get("stargazers_count", 0),
                metadata={
                    "stars": item.get("stargazers_count"),
                    "forks": item.get("forks_count"),
                    "language": item.get("language"),
                    "updated_at": item.get("updated_at"),
                    "topics": item.get("topics", [])
                }
            )
            results.append(result)
        
        return results

class TavilySearchProvider(BaseSearchProvider):
    """Tavily AI-optimized search provider"""

    def __init__(self, api_key: str):
        super().__init__(api_key, "https://api.tavily.com")

    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search using Tavily API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "query": query.query,
                "max_results": min(query.max_results, 20),
                "search_depth": "advanced",
                "include_answer": True,
                "include_raw_content": False
            }

            async with self.session.post(
                f"{self.base_url}/search",
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_tavily_results(data)
                else:
                    logger.error(f"Tavily API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Tavily search error: {e}")
            return []

    def _parse_tavily_results(self, data: Dict) -> List[SearchResult]:
        """Parse Tavily API response"""
        results = []

        for item in data.get("results", []):
            result = SearchResult(
                title=item.get("title", ""),
                url=item.get("url", ""),
                snippet=item.get("content", ""),
                source="tavily",
                score=item.get("score", 0),
                metadata={
                    "published_date": item.get("published_date"),
                    "raw_content": item.get("raw_content", "")
                }
            )
            results.append(result)

        return results

class BingSearchProvider(BaseSearchProvider):
    """Bing Search API provider"""

    def __init__(self, api_key: str):
        super().__init__(api_key, "https://api.bing.microsoft.com/v7.0")

    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search using Bing API"""
        try:
            headers = {
                "Ocp-Apim-Subscription-Key": self.api_key
            }

            params = {
                "q": query.query,
                "count": min(query.max_results, 50),
                "mkt": "en-US" if query.language == "en" else "zh-CN",
                "responseFilter": "Webpages"
            }

            async with self.session.get(
                f"{self.base_url}/search",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_bing_results(data)
                else:
                    logger.error(f"Bing API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"Bing search error: {e}")
            return []

    def _parse_bing_results(self, data: Dict) -> List[SearchResult]:
        """Parse Bing API response"""
        results = []

        webpages = data.get("webPages", {}).get("value", [])
        for item in webpages:
            result = SearchResult(
                title=item.get("name", ""),
                url=item.get("url", ""),
                snippet=item.get("snippet", ""),
                source="bing",
                score=0,  # Bing doesn't provide scores
                metadata={
                    "date_last_crawled": item.get("dateLastCrawled"),
                    "display_url": item.get("displayUrl")
                }
            )
            results.append(result)

        return results

class DuckDuckGoSearchProvider(BaseSearchProvider):
    """DuckDuckGo search provider (no API key required)"""

    def __init__(self):
        super().__init__("", "https://api.duckduckgo.com")

    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search using DuckDuckGo (simplified implementation)"""
        try:
            # Note: DuckDuckGo doesn't have a traditional API
            # This is a simplified implementation for demonstration
            params = {
                "q": query.query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1"
            }

            async with self.session.get(
                f"{self.base_url}/",
                params=params
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_duckduckgo_results(data, query.query)
                else:
                    logger.error(f"DuckDuckGo API error: {response.status}")
                    return []

        except Exception as e:
            logger.error(f"DuckDuckGo search error: {e}")
            return []

    def _parse_duckduckgo_results(self, data: Dict, query: str) -> List[SearchResult]:
        """Parse DuckDuckGo API response"""
        results = []

        # DuckDuckGo instant answer
        if data.get("Abstract"):
            result = SearchResult(
                title=data.get("Heading", query),
                url=data.get("AbstractURL", ""),
                snippet=data.get("Abstract", ""),
                source="duckduckgo",
                score=1.0,
                metadata={
                    "type": "instant_answer",
                    "source": data.get("AbstractSource")
                }
            )
            results.append(result)

        # Related topics
        for topic in data.get("RelatedTopics", []):
            if isinstance(topic, dict) and topic.get("Text"):
                result = SearchResult(
                    title=topic.get("Text", "").split(" - ")[0],
                    url=topic.get("FirstURL", ""),
                    snippet=topic.get("Text", ""),
                    source="duckduckgo",
                    score=0.5,
                    metadata={
                        "type": "related_topic"
                    }
                )
                results.append(result)

        return results[:query.max_results]

class ProductHuntSearchProvider(BaseSearchProvider):
    """Product Hunt API search provider"""

    def __init__(self, api_key: str):
        super().__init__(api_key, "https://api.producthunt.com/v2")
    
    async def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search Product Hunt posts"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # GraphQL query for Product Hunt
            graphql_query = """
            query($search: String!, $first: Int!) {
                posts(search: $search, first: $first) {
                    edges {
                        node {
                            id
                            name
                            tagline
                            description
                            url
                            votesCount
                            createdAt
                            website
                            topics {
                                edges {
                                    node {
                                        name
                                    }
                                }
                            }
                        }
                    }
                }
            }
            """
            
            payload = {
                "query": graphql_query,
                "variables": {
                    "search": query.query,
                    "first": min(query.max_results, 50)
                }
            }
            
            async with self.session.post(
                f"{self.base_url}/graphql",
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_producthunt_results(data)
                else:
                    logger.error(f"Product Hunt API error: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"Product Hunt search error: {e}")
            return []
    
    def _parse_producthunt_results(self, data: Dict) -> List[SearchResult]:
        """Parse Product Hunt API response"""
        results = []
        
        posts = data.get("data", {}).get("posts", {}).get("edges", [])
        for edge in posts:
            node = edge.get("node", {})
            
            topics = [t["node"]["name"] for t in node.get("topics", {}).get("edges", [])]
            
            result = SearchResult(
                title=node.get("name", ""),
                url=node.get("website", node.get("url", "")),
                snippet=node.get("tagline", ""),
                source="producthunt",
                score=node.get("votesCount", 0),
                metadata={
                    "votes": node.get("votesCount"),
                    "description": node.get("description"),
                    "created_at": node.get("createdAt"),
                    "topics": topics
                }
            )
            results.append(result)
        
        return results

class SearchEngine:
    """Main search engine that aggregates results from multiple providers"""
    
    def __init__(self):
        self.config = get_model_config()
        self.providers = {}
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize available search providers"""
        import os

        search_engines = self.config.get_search_engines()

        # Initialize Serper if available
        serper_config = search_engines.get("serper", {})
        serper_key = serper_config.get("env_key")
        if serper_key:
            api_key = os.getenv(serper_key)
            if api_key:
                self.providers["serper"] = SerperSearchProvider(api_key)
                logger.info("Serper search provider initialized")

        # Initialize Tavily if available
        tavily_config = search_engines.get("tavily", {})
        tavily_key = tavily_config.get("env_key")
        if tavily_key:
            api_key = os.getenv(tavily_key)
            if api_key:
                self.providers["tavily"] = TavilySearchProvider(api_key)
                logger.info("Tavily search provider initialized")

        # Initialize Bing if available
        bing_config = search_engines.get("bing", {})
        bing_key = bing_config.get("env_key")
        if bing_key:
            api_key = os.getenv(bing_key)
            if api_key:
                self.providers["bing"] = BingSearchProvider(api_key)
                logger.info("Bing search provider initialized")

        # Initialize DuckDuckGo (no API key required)
        duckduckgo_config = search_engines.get("duckduckgo", {})
        if duckduckgo_config.get("no_auth_required"):
            self.providers["duckduckgo"] = DuckDuckGoSearchProvider()
            logger.info("DuckDuckGo search provider initialized")

        # Initialize GitHub if available
        data_sources = self.config.get_data_sources()
        github_config = data_sources.get("github", {})
        github_key = github_config.get("env_key")
        if github_key:
            api_key = os.getenv(github_key)
            if api_key:
                self.providers["github"] = GitHubSearchProvider(api_key)
                logger.info("GitHub search provider initialized")

        # Initialize Product Hunt if available
        ph_config = data_sources.get("producthunt", {})
        ph_key = ph_config.get("env_key")
        if ph_key:
            api_key = os.getenv(ph_key)
            if api_key:
                self.providers["producthunt"] = ProductHuntSearchProvider(api_key)
                logger.info("Product Hunt search provider initialized")

        logger.info(f"Initialized {len(self.providers)} search providers")
    
    async def search(self, query: SearchQuery) -> SearchResponse:
        """Perform comprehensive search across all available sources"""
        start_time = time.time()
        
        try:
            # Determine which sources to search
            sources_to_search = query.sources or list(self.providers.keys())
            available_sources = [s for s in sources_to_search if s in self.providers]
            
            if not available_sources:
                return SearchResponse(
                    query=query,
                    results=[],
                    total_results=0,
                    search_time=0,
                    sources_used=[],
                    success=False,
                    error_message="No search providers available"
                )
            
            # Execute searches in parallel
            search_tasks = []
            for source in available_sources:
                provider = self.providers[source]
                task = self._search_with_provider(provider, query)
                search_tasks.append(task)
            
            # Wait for all searches to complete
            search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
            
            # Aggregate results
            all_results = []
            for i, results in enumerate(search_results):
                if isinstance(results, Exception):
                    logger.error(f"Search failed for {available_sources[i]}: {results}")
                    continue
                all_results.extend(results)
            
            # Sort and deduplicate results
            final_results = self._process_results(all_results, query.max_results)
            
            search_time = time.time() - start_time
            
            return SearchResponse(
                query=query,
                results=final_results,
                total_results=len(final_results),
                search_time=search_time,
                sources_used=available_sources,
                success=True
            )
            
        except Exception as e:
            search_time = time.time() - start_time
            logger.error(f"Search engine error: {e}")
            
            return SearchResponse(
                query=query,
                results=[],
                total_results=0,
                search_time=search_time,
                sources_used=[],
                success=False,
                error_message=str(e)
            )
    
    async def _search_with_provider(self, provider: BaseSearchProvider, query: SearchQuery) -> List[SearchResult]:
        """Search with a single provider"""
        async with provider:
            return await provider.search(query)
    
    def _process_results(self, results: List[SearchResult], max_results: int) -> List[SearchResult]:
        """Process and deduplicate search results"""
        # Remove duplicates based on URL
        seen_urls = set()
        unique_results = []
        
        for result in results:
            if result.url not in seen_urls:
                seen_urls.add(result.url)
                unique_results.append(result)
        
        # Sort by relevance score (higher is better)
        unique_results.sort(key=lambda x: x.score, reverse=True)
        
        # Limit results
        return unique_results[:max_results]
    
    def get_available_sources(self) -> List[str]:
        """Get list of available search sources"""
        return list(self.providers.keys())

# Factory function
def create_search_engine() -> SearchEngine:
    """Create and return a search engine instance"""
    return SearchEngine()

# Utility functions for common search patterns
def create_competitor_search_query(keywords: List[str], language: str = "en") -> SearchQuery:
    """Create a search query optimized for competitor research"""
    # Combine keywords with competitor-focused terms
    competitor_terms = ["alternative", "competitor", "vs", "comparison", "review", "best"]
    
    if language == "zh":
        competitor_terms = ["替代", "竞品", "对比", "比较", "评测", "最佳"]
    
    query_parts = keywords + competitor_terms
    query_string = " ".join(query_parts)
    
    return SearchQuery(
        query=query_string,
        language=language,
        max_results=20,
        sources=["web", "github", "producthunt"],
        time_range="year"  # Focus on recent results
    )

def create_market_research_query(domain: str, language: str = "en") -> SearchQuery:
    """Create a search query optimized for market research"""
    market_terms = ["market size", "industry report", "trends", "analysis", "statistics"]
    
    if language == "zh":
        market_terms = ["市场规模", "行业报告", "趋势", "分析", "统计"]
    
    query_string = f"{domain} {' '.join(market_terms)}"
    
    return SearchQuery(
        query=query_string,
        language=language,
        max_results=15,
        sources=["web"],
        time_range="year"
    )
