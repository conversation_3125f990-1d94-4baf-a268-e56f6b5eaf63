# 部署指南 / Deployment Guide

## 🚀 快速部署到 Hugging Face Spaces

### 1. 准备工作

#### 1.1 创建 Hugging Face 账户
- 访问 [Hugging Face](https://huggingface.co/) 并注册账户
- 验证邮箱并完善个人资料

#### 1.2 创建新的 Space
1. 点击右上角头像 → "New Space"
2. 填写 Space 信息：
   - **Space name**: `insight-pulse` 或 `explorer-agent`
   - **License**: MIT
   - **SDK**: Gradio
   - **Hardware**: CPU basic (免费)
   - **Visibility**: Public

### 2. 上传项目文件

#### 2.1 必需文件列表
```
insight-pulse/
├── app_demo.py          # 主应用文件（演示版）
├── requirements.txt     # 依赖列表
├── README.md           # 项目说明
├── utils/              # 工具模块
│   ├── __init__.py
│   ├── config.py
│   ├── logger.py
│   └── retry.py
├── i18n/               # 国际化资源
│   ├── zh.json
│   ├── en.json
│   └── loader.py
├── llm/                # LLM接口层
│   ├── __init__.py
│   ├── base.py
│   └── openai_client.py
├── core/               # 核心业务逻辑
│   ├── __init__.py
│   └── demand_parser.py
└── .env.example        # 环境变量示例
```

#### 2.2 上传方式

**方式一：Web界面上传**
1. 在 Space 页面点击 "Files" 标签
2. 点击 "Upload files" 
3. 拖拽或选择所有项目文件上传

**方式二：Git 命令行**
```bash
git clone https://huggingface.co/spaces/YOUR_USERNAME/insight-pulse
cd insight-pulse
# 复制项目文件到此目录
git add .
git commit -m "Initial commit"
git push
```

### 3. 配置文件

#### 3.1 创建 README.md
在 Space 根目录创建 README.md：

```yaml
---
title: 逛逛 (InsightPulse)
emoji: 🔍
colorFrom: blue
colorTo: purple
sdk: gradio
sdk_version: 4.44.1
app_file: app_demo.py
pinned: false
license: mit
---

# 逛逛 (InsightPulse) - 需求探索与可行性分析平台

智能分析您的需求，发现现有解决方案，评估创新机会。

## 功能特点
- 🔍 智能需求解析
- 🌐 生态扫描分析  
- 💡 创新机会评估
- 📊 可视化展示
- 🌍 中英文双语支持

## 使用方法
1. 选择使用模式（普通用户/开发者）
2. 输入您的需求描述
3. 点击"开始分析"获取结果

*注：这是演示版本，完整版本将集成大语言模型。*
```

#### 3.2 requirements.txt
确保包含所有必需依赖：

```txt
gradio>=4.0.0
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0
requests>=2.31.0
```

### 4. 环境变量配置（可选）

如果需要使用 LLM 功能，在 Space 设置中添加环境变量：

1. 进入 Space 设置页面
2. 点击 "Variables and secrets"
3. 添加以下变量：
   - `OPENAI_API_KEY`: 您的 OpenAI API 密钥
   - `DEFAULT_LANGUAGE`: zh
   - `DEBUG`: false

### 5. 部署验证

#### 5.1 检查部署状态
- Space 页面会显示构建日志
- 等待状态从 "Building" 变为 "Running"
- 如有错误，查看 "Logs" 标签页

#### 5.2 功能测试
1. 访问 Space URL
2. 测试语言切换功能
3. 测试不同模式的需求分析
4. 验证示例输入是否正常工作

### 6. 常见问题

#### 6.1 构建失败
- 检查 requirements.txt 中的依赖版本
- 确保所有 Python 文件语法正确
- 查看构建日志中的错误信息

#### 6.2 应用无法启动
- 检查 app_demo.py 中的导入路径
- 确保所有必需文件都已上传
- 验证文件结构是否正确

#### 6.3 功能异常
- 检查浏览器控制台是否有错误
- 查看 Space 的运行日志
- 确认环境变量配置正确

### 7. 性能优化

#### 7.1 减少启动时间
- 移除不必要的依赖
- 优化导入语句
- 使用懒加载

#### 7.2 提升用户体验
- 添加加载动画
- 优化界面响应速度
- 增加错误处理

### 8. 升级到完整版

要启用完整的 LLM 功能：

1. 获取 OpenAI API 密钥
2. 在环境变量中配置 API 密钥
3. 将 `app_demo.py` 替换为 `app.py`
4. 更新 README.md 中的 `app_file` 配置

### 9. 监控和维护

#### 9.1 使用统计
- 在 Space 页面查看访问统计
- 监控用户反馈和评论

#### 9.2 定期更新
- 更新依赖版本
- 修复发现的问题
- 添加新功能

### 10. 社区分享

#### 10.1 推广 Space
- 在社交媒体分享
- 参与 Hugging Face 社区讨论
- 撰写技术博客

#### 10.2 收集反馈
- 启用 Space 评论功能
- 创建 GitHub 仓库收集 Issues
- 建立用户交流群

---

## 🎯 部署检查清单

- [ ] Hugging Face 账户已创建
- [ ] Space 已创建并配置
- [ ] 所有项目文件已上传
- [ ] README.md 配置正确
- [ ] requirements.txt 包含所有依赖
- [ ] 应用成功启动
- [ ] 基本功能测试通过
- [ ] 界面显示正常
- [ ] 中英文切换正常
- [ ] 示例功能正常

## 📞 技术支持

如遇到部署问题，可以：
1. 查看 Hugging Face Spaces 官方文档
2. 在项目 GitHub 仓库提交 Issue
3. 联系开发团队

祝您部署顺利！🚀
