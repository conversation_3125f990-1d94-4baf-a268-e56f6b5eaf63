"""
OpenAI LLM client implementation for InsightPulse platform.
Supports GPT models with proper error handling and retry mechanisms.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from typing import Dict, List, Any, Optional
import openai
from openai import OpenAI
import time

from .base import BaseLLMClient, LLMProvider, LLMMessage, LLMResponse, LLMConfig
from utils.logger import get_api_logger

logger = get_api_logger("LLM.OpenAI")

class OpenAIClient(BaseLLMClient):
    """OpenAI GPT client implementation"""
    
    def __init__(self, api_key: str, config: Optional[LLMConfig] = None):
        super().__init__(api_key, config)
        self.client = OpenAI(api_key=api_key)
        
        # Default models
        self.available_models = [
            "gpt-4o",
            "gpt-4o-mini", 
            "gpt-4-turbo",
            "gpt-4",
            "gpt-3.5-turbo"
        ]
        
        # Set default model if not specified
        if not config or not config.model:
            self.config.model = "gpt-4o-mini"
    
    def _get_provider(self) -> LLMProvider:
        """Get the provider enum"""
        return LLMProvider.OPENAI
    
    def _prepare_messages(self, messages: List[LLMMessage]) -> List[Dict[str, str]]:
        """Convert LLMMessage objects to OpenAI format"""
        openai_messages = []
        
        for msg in messages:
            openai_msg = {
                "role": msg.role,
                "content": msg.content
            }
            openai_messages.append(openai_msg)
        
        return openai_messages
    
    def _make_request(self, messages: List[Dict[str, str]], config: LLMConfig) -> Dict[str, Any]:
        """Make request to OpenAI API"""
        try:
            response = self.client.chat.completions.create(
                model=config.model,
                messages=messages,
                max_tokens=config.max_tokens,
                temperature=config.temperature,
                top_p=config.top_p,
                frequency_penalty=config.frequency_penalty,
                presence_penalty=config.presence_penalty,
                timeout=config.timeout
            )
            
            # Convert response to dict for consistent handling
            return {
                "choices": [
                    {
                        "message": {
                            "content": response.choices[0].message.content,
                            "role": response.choices[0].message.role
                        },
                        "finish_reason": response.choices[0].finish_reason
                    }
                ],
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                    "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                    "total_tokens": response.usage.total_tokens if response.usage else 0
                },
                "model": response.model,
                "id": response.id
            }
            
        except openai.APIError as e:
            logger.error(f"OpenAI API error: {e}")
            raise
        except openai.APIConnectionError as e:
            logger.error(f"OpenAI connection error: {e}")
            raise
        except openai.RateLimitError as e:
            logger.error(f"OpenAI rate limit error: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected OpenAI error: {e}")
            raise
    
    def _parse_response(self, response: Dict[str, Any]) -> LLMResponse:
        """Parse OpenAI response into standard format"""
        try:
            content = response["choices"][0]["message"]["content"]
            usage = response.get("usage", {})
            
            return LLMResponse(
                content=content,
                provider=self.provider.value,
                model=response.get("model", self.config.model),
                usage=usage,
                metadata={
                    "finish_reason": response["choices"][0].get("finish_reason"),
                    "response_id": response.get("id")
                },
                success=True
            )
            
        except (KeyError, IndexError) as e:
            logger.error(f"Failed to parse OpenAI response: {e}")
            return LLMResponse(
                content="",
                provider=self.provider.value,
                model=self.config.model,
                success=False,
                error=f"Response parsing error: {e}"
            )
    
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        return self.available_models
    
    def validate_model(self, model: str) -> bool:
        """Validate if model is available"""
        return model in self.available_models
    
    def estimate_tokens(self, text: str) -> int:
        """Rough estimation of token count"""
        # Simple estimation: ~4 characters per token for English, ~2 for Chinese
        # This is a rough approximation
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        other_chars = len(text) - chinese_chars
        
        estimated_tokens = (chinese_chars // 2) + (other_chars // 4)
        return max(1, estimated_tokens)
    
    def create_system_message(self, content: str, language: str = "zh") -> LLMMessage:
        """Create a system message with language context"""
        if language == "zh":
            system_content = f"你是一个专业的需求分析助手。请用中文回答。{content}"
        else:
            system_content = f"You are a professional demand analysis assistant. Please respond in English. {content}"
        
        return LLMMessage(role="system", content=system_content)
    
    def create_user_message(self, content: str, language: str = "zh") -> LLMMessage:
        """Create a user message with language hint"""
        if language == "zh":
            language_hint = "请用中文回答："
        else:
            language_hint = "Please respond in English:"
        
        return LLMMessage(role="user", content=f"{language_hint}\n{content}")

def create_openai_client(api_key: str, model: str = "gpt-4o-mini") -> OpenAIClient:
    """Factory function to create OpenAI client"""
    config = LLMConfig(model=model)
    return OpenAIClient(api_key, config)

# Predefined prompt templates for different analysis tasks
class OpenAIPrompts:
    """Collection of prompt templates for OpenAI"""
    
    @staticmethod
    def demand_analysis_prompt(language: str = "zh") -> str:
        """Prompt for demand analysis"""
        if language == "zh":
            return """
你是一个专业的需求分析专家。请分析用户的需求并提供以下信息：

1. 用户意图：用户想要什么？
2. 需求领域：属于哪个行业或领域？
3. 关键词：提取3-5个核心关键词
4. 约束条件：有什么限制或特殊要求？
5. 用户模式：判断是"普通用户模式"（寻找现有解决方案）还是"开发者模式"（探索创新机会）

请以JSON格式返回结果。
"""
        else:
            return """
You are a professional demand analysis expert. Please analyze the user's requirements and provide the following information:

1. User Intent: What does the user want?
2. Domain Category: Which industry or field does it belong to?
3. Keywords: Extract 3-5 core keywords
4. Constraints: Any limitations or special requirements?
5. User Mode: Determine if it's "User Mode" (seeking existing solutions) or "Developer Mode" (exploring innovation opportunities)

Please return the result in JSON format.
"""
    
    @staticmethod
    def competitor_analysis_prompt(language: str = "zh") -> str:
        """Prompt for competitor analysis"""
        if language == "zh":
            return """
你是一个市场分析专家。基于提供的竞品信息，请进行分析并提供：

1. 竞品概述：简要描述每个竞品
2. 功能对比：核心功能比较
3. 优劣势分析：各自的优势和劣势
4. 市场定位：目标用户群体
5. 差异化机会：可能的创新点或改进方向

请用清晰的结构化格式回答。
"""
        else:
            return """
You are a market analysis expert. Based on the provided competitor information, please analyze and provide:

1. Competitor Overview: Brief description of each competitor
2. Feature Comparison: Core functionality comparison
3. Pros and Cons Analysis: Advantages and disadvantages of each
4. Market Positioning: Target user groups
5. Differentiation Opportunities: Possible innovation points or improvement directions

Please respond in a clear, structured format.
"""
    
    @staticmethod
    def decision_recommendation_prompt(language: str = "zh") -> str:
        """Prompt for decision recommendations"""
        if language == "zh":
            return """
你是一个商业决策顾问。基于需求分析和市场调研结果，请提供明确的行动建议：

1. 推荐行动：🚫 放弃 / 💎 推荐新方向 / ⚠️ 高风险警告 / 🔧 优化建议
2. 分析理由：详细说明推荐的原因
3. 下一步行动：具体的执行建议
4. 风险提醒：需要注意的潜在风险

请提供实用且可操作的建议。
"""
        else:
            return """
You are a business decision consultant. Based on demand analysis and market research results, please provide clear action recommendations:

1. Recommended Action: 🚫 Abandon / 💎 New Direction / ⚠️ High Risk Warning / 🔧 Optimization
2. Analysis Reasoning: Detailed explanation of the recommendation
3. Next Steps: Specific execution suggestions
4. Risk Warning: Potential risks to be aware of

Please provide practical and actionable advice.
"""
