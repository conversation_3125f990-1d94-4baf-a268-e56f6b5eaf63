"""
Demand Parser Engine for InsightPulse platform.
Analyzes user input to extract intent, categorize domains, and identify user modes.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import json
import re
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from llm.base import <PERSON>MManager, LLMMessage, LLMConfig
from llm.openai_client import OpenAIPrompts
from utils.logger import get_logger
from utils.retry import llm_retry

logger = get_logger("DemandParser")

class UserMode(Enum):
    """User interaction modes"""
    USER = "user"  # Seeking existing solutions
    DEVELOPER = "developer"  # Exploring innovation opportunities

class DemandCategory(Enum):
    """Demand categories"""
    PRODUCTIVITY = "productivity"  # 效率工具
    EDUCATION = "education"  # 教育
    ENTERTAINMENT = "entertainment"  # 娱乐
    HEALTH = "health"  # 健康
    FINANCE = "finance"  # 金融
    COMMUNICATION = "communication"  # 通讯
    DEVELOPMENT = "development"  # 开发工具
    BUSINESS = "business"  # 商业
    LIFESTYLE = "lifestyle"  # 生活方式
    TECHNOLOGY = "technology"  # 技术
    OTHER = "other"  # 其他

@dataclass
class DemandAnalysis:
    """Result of demand analysis"""
    user_intent: str
    category: DemandCategory
    keywords: List[str]
    constraints: List[str]
    user_mode: UserMode
    confidence_score: float
    language: str
    raw_input: str
    metadata: Optional[Dict[str, Any]] = None

class DemandParser:
    """Main demand parsing engine"""
    
    def __init__(self, llm_manager: LLMManager):
        self.llm_manager = llm_manager
        
        # Keyword patterns for quick classification
        self.category_keywords = {
            DemandCategory.PRODUCTIVITY: [
                "笔记", "note", "效率", "productivity", "办公", "office", "管理", "management",
                "任务", "task", "项目", "project", "协作", "collaboration", "文档", "document"
            ],
            DemandCategory.DEVELOPMENT: [
                "开发", "develop", "编程", "programming", "代码", "code", "API", "框架", "framework",
                "工具", "tool", "IDE", "编辑器", "editor", "调试", "debug", "测试", "test"
            ],
            DemandCategory.EDUCATION: [
                "学习", "learn", "教育", "education", "课程", "course", "培训", "training",
                "知识", "knowledge", "技能", "skill", "教学", "teaching"
            ],
            DemandCategory.ENTERTAINMENT: [
                "游戏", "game", "娱乐", "entertainment", "音乐", "music", "视频", "video",
                "电影", "movie", "直播", "streaming", "社交", "social"
            ],
            DemandCategory.HEALTH: [
                "健康", "health", "医疗", "medical", "运动", "exercise", "健身", "fitness",
                "饮食", "diet", "心理", "mental", "睡眠", "sleep"
            ],
            DemandCategory.FINANCE: [
                "金融", "finance", "投资", "investment", "理财", "money", "银行", "bank",
                "支付", "payment", "交易", "trading", "预算", "budget"
            ]
        }
        
        # Developer mode indicators
        self.developer_indicators = [
            "开发", "develop", "创建", "create", "构建", "build", "设计", "design",
            "实现", "implement", "制作", "make", "搭建", "setup", "编写", "write",
            "AI", "机器学习", "machine learning", "区块链", "blockchain", "APP", "应用"
        ]
        
        # User mode indicators
        self.user_indicators = [
            "推荐", "recommend", "寻找", "find", "需要", "need", "想要", "want",
            "有什么", "what", "哪个", "which", "好用", "useful", "最好", "best"
        ]
    
    def parse_demand(self, user_input: str, language: str = "zh") -> DemandAnalysis:
        """
        Parse user demand and extract structured information
        
        Args:
            user_input: Raw user input text
            language: Target language for analysis
            
        Returns:
            DemandAnalysis: Structured analysis result
        """
        logger.info(f"Parsing demand: {user_input[:50]}...")
        
        try:
            # Quick classification using keywords
            quick_analysis = self._quick_classify(user_input, language)
            
            # LLM-based detailed analysis
            llm_analysis = self._llm_analyze(user_input, language)
            
            # Combine results
            final_analysis = self._combine_analysis(quick_analysis, llm_analysis, user_input, language)
            
            logger.info(f"Demand parsed successfully: {final_analysis.user_mode.value} mode, {final_analysis.category.value} category")
            return final_analysis
            
        except Exception as e:
            logger.error(f"Failed to parse demand: {e}")
            # Return fallback analysis
            return self._create_fallback_analysis(user_input, language)
    
    def _quick_classify(self, user_input: str, language: str) -> Dict[str, Any]:
        """Quick classification using keyword matching"""
        input_lower = user_input.lower()
        
        # Determine user mode
        developer_score = sum(1 for indicator in self.developer_indicators if indicator in input_lower)
        user_score = sum(1 for indicator in self.user_indicators if indicator in input_lower)
        
        if developer_score > user_score:
            user_mode = UserMode.DEVELOPER
        else:
            user_mode = UserMode.USER
        
        # Determine category
        category_scores = {}
        for category, keywords in self.category_keywords.items():
            score = sum(1 for keyword in keywords if keyword in input_lower)
            if score > 0:
                category_scores[category] = score
        
        if category_scores:
            category = max(category_scores, key=category_scores.get)
        else:
            category = DemandCategory.OTHER
        
        # Extract basic keywords
        keywords = self._extract_keywords(user_input)
        
        return {
            "user_mode": user_mode,
            "category": category,
            "keywords": keywords,
            "confidence": 0.6  # Medium confidence for keyword-based analysis
        }
    
    def _llm_analyze(self, user_input: str, language: str) -> Dict[str, Any]:
        """Detailed analysis using LLM"""
        try:
            # Prepare messages
            system_prompt = OpenAIPrompts.demand_analysis_prompt(language)
            messages = [
                LLMMessage(role="system", content=system_prompt),
                LLMMessage(role="user", content=user_input)
            ]
            
            # Configure for structured output
            config = LLMConfig(
                model="gpt-4o-mini",
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=1000,
                language=language
            )
            
            # Generate analysis
            response = self.llm_manager.generate_with_fallback(messages, config)
            
            if response.success:
                # Parse JSON response
                analysis_data = self._parse_llm_response(response.content)
                return analysis_data
            else:
                logger.error(f"LLM analysis failed: {response.error}")
                return {"confidence": 0.0}
                
        except Exception as e:
            logger.error(f"LLM analysis error: {e}")
            return {"confidence": 0.0}
    
    def _parse_llm_response(self, response_content: str) -> Dict[str, Any]:
        """Parse LLM response and extract structured data"""
        try:
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                data = json.loads(json_str)
            else:
                # Fallback: parse structured text
                data = self._parse_structured_text(response_content)
            
            # Normalize the data
            normalized_data = self._normalize_llm_data(data)
            normalized_data["confidence"] = 0.9  # High confidence for LLM analysis
            
            return normalized_data
            
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {e}")
            return {"confidence": 0.0}
    
    def _normalize_llm_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize LLM response data to standard format"""
        normalized = {}
        
        # Extract user intent
        normalized["user_intent"] = data.get("user_intent", data.get("intent", ""))
        
        # Extract and normalize category
        category_str = data.get("category", data.get("domain", "")).lower()
        normalized["category"] = self._map_category_string(category_str)
        
        # Extract keywords
        keywords = data.get("keywords", [])
        if isinstance(keywords, str):
            keywords = [k.strip() for k in keywords.split(",")]
        normalized["keywords"] = keywords[:5]  # Limit to 5 keywords
        
        # Extract constraints
        constraints = data.get("constraints", [])
        if isinstance(constraints, str):
            constraints = [constraints]
        normalized["constraints"] = constraints
        
        # Extract and normalize user mode
        mode_str = data.get("user_mode", data.get("mode", "")).lower()
        if "developer" in mode_str or "开发" in mode_str:
            normalized["user_mode"] = UserMode.DEVELOPER
        else:
            normalized["user_mode"] = UserMode.USER
        
        return normalized
    
    def _map_category_string(self, category_str: str) -> DemandCategory:
        """Map category string to DemandCategory enum"""
        category_mapping = {
            "productivity": DemandCategory.PRODUCTIVITY,
            "效率": DemandCategory.PRODUCTIVITY,
            "办公": DemandCategory.PRODUCTIVITY,
            "development": DemandCategory.DEVELOPMENT,
            "开发": DemandCategory.DEVELOPMENT,
            "编程": DemandCategory.DEVELOPMENT,
            "education": DemandCategory.EDUCATION,
            "教育": DemandCategory.EDUCATION,
            "entertainment": DemandCategory.ENTERTAINMENT,
            "娱乐": DemandCategory.ENTERTAINMENT,
            "health": DemandCategory.HEALTH,
            "健康": DemandCategory.HEALTH,
            "finance": DemandCategory.FINANCE,
            "金融": DemandCategory.FINANCE,
            "communication": DemandCategory.COMMUNICATION,
            "通讯": DemandCategory.COMMUNICATION,
            "business": DemandCategory.BUSINESS,
            "商业": DemandCategory.BUSINESS,
            "lifestyle": DemandCategory.LIFESTYLE,
            "生活": DemandCategory.LIFESTYLE,
            "technology": DemandCategory.TECHNOLOGY,
            "技术": DemandCategory.TECHNOLOGY
        }
        
        for key, category in category_mapping.items():
            if key in category_str:
                return category
        
        return DemandCategory.OTHER
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text using simple heuristics"""
        # Remove common stop words
        stop_words = {
            "的", "是", "在", "有", "和", "与", "或", "但", "如果", "因为", "所以",
            "the", "is", "in", "and", "or", "but", "if", "because", "so", "a", "an"
        }
        
        # Simple tokenization and filtering
        words = re.findall(r'\b\w+\b', text.lower())
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        
        # Return top 5 most relevant keywords
        return keywords[:5]
    
    def _parse_structured_text(self, text: str) -> Dict[str, Any]:
        """Parse structured text response when JSON parsing fails"""
        data = {}
        
        # Extract intent
        intent_match = re.search(r'(?:用户意图|intent)[：:]\s*(.+)', text, re.IGNORECASE)
        if intent_match:
            data["user_intent"] = intent_match.group(1).strip()
        
        # Extract category
        category_match = re.search(r'(?:需求领域|category|domain)[：:]\s*(.+)', text, re.IGNORECASE)
        if category_match:
            data["category"] = category_match.group(1).strip()
        
        # Extract keywords
        keywords_match = re.search(r'(?:关键词|keywords)[：:]\s*(.+)', text, re.IGNORECASE)
        if keywords_match:
            keywords_str = keywords_match.group(1).strip()
            data["keywords"] = [k.strip() for k in re.split(r'[,，、]', keywords_str)]
        
        return data
    
    def _combine_analysis(self, quick_analysis: Dict[str, Any], llm_analysis: Dict[str, Any], 
                         user_input: str, language: str) -> DemandAnalysis:
        """Combine quick and LLM analysis results"""
        
        # Use LLM analysis if available and confident, otherwise fall back to quick analysis
        if llm_analysis.get("confidence", 0) > 0.7:
            primary_analysis = llm_analysis
            secondary_analysis = quick_analysis
            confidence = llm_analysis["confidence"]
        else:
            primary_analysis = quick_analysis
            secondary_analysis = llm_analysis
            confidence = quick_analysis["confidence"]
        
        # Combine keywords from both analyses
        keywords = list(set(
            primary_analysis.get("keywords", []) + 
            secondary_analysis.get("keywords", [])
        ))[:5]
        
        return DemandAnalysis(
            user_intent=primary_analysis.get("user_intent", ""),
            category=primary_analysis.get("category", DemandCategory.OTHER),
            keywords=keywords,
            constraints=primary_analysis.get("constraints", []),
            user_mode=primary_analysis.get("user_mode", UserMode.USER),
            confidence_score=confidence,
            language=language,
            raw_input=user_input,
            metadata={
                "quick_analysis": quick_analysis,
                "llm_analysis": llm_analysis
            }
        )
    
    def _create_fallback_analysis(self, user_input: str, language: str) -> DemandAnalysis:
        """Create fallback analysis when all methods fail"""
        return DemandAnalysis(
            user_intent="General inquiry",
            category=DemandCategory.OTHER,
            keywords=self._extract_keywords(user_input),
            constraints=[],
            user_mode=UserMode.USER,
            confidence_score=0.3,
            language=language,
            raw_input=user_input,
            metadata={"fallback": True}
        )
