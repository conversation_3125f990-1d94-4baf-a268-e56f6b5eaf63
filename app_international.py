"""
International version of InsightPulse platform.
Optimized for Hugging Face Spaces with proper language switching and model selection.
"""

import gradio as gr
import os
import json
from typing import Optional, Tuple, Dict, Any
import time

# Import our modules
from utils.config import get_config
from utils.logger import get_logger
from i18n.loader import get_i18n, t, set_language
from core.demand_parser import UserMode, DemandCategory
from app_demo import DEMO_SOLUTIONS, simulate_demand_analysis

# Initialize components
config = get_config()
logger = get_logger("InternationalApp")
i18n = get_i18n()

# Available model providers
MODEL_PROVIDERS = {
    "openai": {
        "name": "OpenAI",
        "models": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"],
        "env_key": "OPENAI_API_KEY",
        "description": "Most popular AI models with excellent performance"
    },
    "anthropic": {
        "name": "Anthropic Claude",
        "models": ["claude-3-5-sonnet-20241022", "claude-3-haiku-20240307", "claude-3-opus-20240229"],
        "env_key": "ANTHROPIC_API_KEY",
        "description": "Advanced reasoning and analysis capabilities"
    },
    "google": {
        "name": "Google Gemini",
        "models": ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision"],
        "env_key": "GOOGLE_API_KEY",
        "description": "Google's multimodal AI with strong reasoning"
    },
    "deepseek": {
        "name": "DeepSeek",
        "models": ["deepseek-chat", "deepseek-coder", "deepseek-math"],
        "env_key": "DEEPSEEK_API_KEY",
        "description": "Chinese AI with strong coding and math capabilities"
    },
    "openrouter": {
        "name": "OpenRouter",
        "models": [
            "openai/gpt-4o", "anthropic/claude-3.5-sonnet", "google/gemini-pro",
            "meta-llama/llama-3.1-405b", "mistralai/mixtral-8x7b", "cohere/command-r-plus"
        ],
        "env_key": "OPENROUTER_API_KEY",
        "description": "Access to multiple AI models through one API"
    },
    "cohere": {
        "name": "Cohere",
        "models": ["command-r-plus", "command-r", "command"],
        "env_key": "COHERE_API_KEY",
        "description": "Enterprise-focused AI with strong RAG capabilities"
    },
    "mistral": {
        "name": "Mistral AI",
        "models": ["mistral-large", "mistral-medium", "mistral-small", "mixtral-8x7b"],
        "env_key": "MISTRAL_API_KEY",
        "description": "European AI with multilingual capabilities"
    },
    "together": {
        "name": "Together AI",
        "models": [
            "meta-llama/Llama-3-70b-chat-hf", "mistralai/Mixtral-8x7B-Instruct-v0.1",
            "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO"
        ],
        "env_key": "TOGETHER_API_KEY",
        "description": "Open source models with fast inference"
    },
    "groq": {
        "name": "Groq",
        "models": ["llama3-70b-8192", "llama3-8b-8192", "mixtral-8x7b-32768", "gemma-7b-it"],
        "env_key": "GROQ_API_KEY",
        "description": "Ultra-fast inference for real-time applications"
    }
}

def get_available_providers():
    """Get list of available providers based on environment variables"""
    available = []
    for provider_id, provider_info in MODEL_PROVIDERS.items():
        env_key = provider_info["env_key"]
        if os.getenv(env_key):
            available.append((provider_info["name"], provider_id))
    
    # Always include demo mode
    available.append(("Demo Mode (No API Required)", "demo"))
    return available

def get_models_for_provider(provider_id):
    """Get available models for a provider"""
    if provider_id == "demo":
        return ["demo-analyzer"]
    return MODEL_PROVIDERS.get(provider_id, {}).get("models", [])

def update_interface_language(language):
    """Update interface elements based on language"""
    set_language(language)
    
    if language == "zh":
        return {
            "title": "🔍 逛逛 (InsightPulse)",
            "subtitle": "需求探索与可行性分析平台",
            "description": "智能分析您的需求，发现现有解决方案，评估创新机会",
            "mode_label": "使用模式",
            "user_mode": "🔍 普通用户模式 - 寻找现有解决方案",
            "developer_mode": "💡 开发者模式 - 探索创新机会",
            "input_label": "需求描述",
            "input_placeholder": "请详细描述您的需求或想法...\n\n示例：\n- 推荐一个好用的笔记软件\n- 开发一个AI驱动的代码审查工具",
            "provider_label": "AI 模型提供商",
            "model_label": "选择模型",
            "submit_button": "🚀 开始分析",
            "clear_button": "🗑️ 清空",
            "demo_notice": "📢 演示说明：这是演示版本，使用模拟数据展示平台功能。完整版本将集成大语言模型进行深度分析。"
        }
    else:
        return {
            "title": "🔍 InsightPulse",
            "subtitle": "Demand Exploration & Feasibility Analysis Platform",
            "description": "Intelligently analyze your requirements, discover existing solutions, and evaluate innovation opportunities",
            "mode_label": "Mode",
            "user_mode": "🔍 User Mode - Find existing solutions",
            "developer_mode": "💡 Developer Mode - Explore innovation opportunities",
            "input_label": "Requirement Description",
            "input_placeholder": "Please describe your requirements or ideas in detail...\n\nExamples:\n- Recommend a good note-taking app\n- Develop an AI-driven code review tool",
            "provider_label": "AI Model Provider",
            "model_label": "Select Model",
            "submit_button": "🚀 Start Analysis",
            "clear_button": "🗑️ Clear",
            "demo_notice": "📢 Demo Notice: This is a demo version using simulated data to showcase platform features. The full version will integrate large language models for deep analysis."
        }

def analyze_demand_international(user_input: str, mode: str, language: str, provider: str, model: str) -> Tuple[str, str, str]:
    """International version of demand analysis"""
    
    if not user_input.strip():
        error_msg = "请输入您的需求描述" if language == "zh" else "Please enter your requirement description"
        return error_msg, "", ""
    
    try:
        # Set language
        set_language(language)
        
        # Log the analysis request
        logger.info(f"Analysis request: provider={provider}, model={model}, language={language}, mode={mode}")
        
        # Simulate processing time
        time.sleep(1)
        
        # For demo, use simulated analysis
        if provider == "demo":
            analysis = simulate_demand_analysis(user_input, language)
        else:
            # TODO: Implement real LLM analysis
            analysis = simulate_demand_analysis(user_input, language)
            analysis["provider"] = provider
            analysis["model"] = model
        
        # Format results
        analysis_result = format_analysis_result(analysis, language, provider, model)
        recommendations = generate_recommendations(analysis, language)
        visualization = generate_visualization(analysis, language)
        
        return analysis_result, recommendations, visualization
        
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        error_msg = "分析过程中出现错误" if language == "zh" else "Error occurred during analysis"
        return error_msg, "", ""

def format_analysis_result(analysis: Dict[str, Any], language: str, provider: str, model: str) -> str:
    """Format analysis result for display"""
    
    if language == "zh":
        mode_text = "开发者模式" if analysis["user_mode"] == UserMode.DEVELOPER else "普通用户模式"
        category_text = {
            DemandCategory.PRODUCTIVITY: "效率工具",
            DemandCategory.DEVELOPMENT: "开发工具",
            DemandCategory.EDUCATION: "教育",
            DemandCategory.ENTERTAINMENT: "娱乐",
            DemandCategory.OTHER: "其他"
        }.get(analysis["category"], "其他")
        
        provider_info = f"**AI 分析引擎**: {MODEL_PROVIDERS.get(provider, {}).get('name', provider)} - {model}"
        if provider == "demo":
            provider_info = "**分析引擎**: 演示模式（关键词分析）"
        
        return f"""
## 📋 需求分析结果

{provider_info}

**用户意图**: {analysis["user_intent"]}

**需求领域**: {category_text}

**关键词**: {', '.join(analysis["keywords"])}

**用户模式**: {mode_text}

**分析置信度**: {analysis["confidence"]:.1%}

---
*分析完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}*
"""
    else:
        mode_text = "Developer Mode" if analysis["user_mode"] == UserMode.DEVELOPER else "User Mode"
        category_text = {
            DemandCategory.PRODUCTIVITY: "Productivity Tools",
            DemandCategory.DEVELOPMENT: "Development Tools", 
            DemandCategory.EDUCATION: "Education",
            DemandCategory.ENTERTAINMENT: "Entertainment",
            DemandCategory.OTHER: "Other"
        }.get(analysis["category"], "Other")
        
        provider_info = f"**AI Analysis Engine**: {MODEL_PROVIDERS.get(provider, {}).get('name', provider)} - {model}"
        if provider == "demo":
            provider_info = "**Analysis Engine**: Demo Mode (Keyword Analysis)"
        
        return f"""
## 📋 Analysis Results

{provider_info}

**User Intent**: {analysis["user_intent"]}

**Domain Category**: {category_text}

**Keywords**: {', '.join(analysis["keywords"])}

**User Mode**: {mode_text}

**Confidence Score**: {analysis["confidence"]:.1%}

---
*Analysis completed at: {time.strftime('%Y-%m-%d %H:%M:%S')}*
"""

def generate_recommendations(analysis: Dict[str, Any], language: str) -> str:
    """Generate recommendations based on analysis"""
    
    category = analysis["category"]
    user_mode = analysis["user_mode"]
    
    if user_mode == UserMode.USER:
        # Show existing solutions
        solutions = DEMO_SOLUTIONS.get(category, {}).get(language, [])
        
        if language == "zh":
            result = "## 🔍 现有解决方案推荐\n\n"
            if solutions:
                for i, solution in enumerate(solutions, 1):
                    result += f"""
### {i}. {solution['name']}
- **评分**: {solution['rating']}
- **核心功能**: {solution['features']}
- **优势**: {solution['pros']}
- **劣势**: {solution['cons']}
- **官网**: [{solution['name']}]({solution['website']})

"""
            else:
                result += "暂无相关解决方案数据。\n"
            
            result += "\n*注：这是演示数据。完整版本将实时搜索最新的解决方案。*"
        else:
            result = "## 🔍 Existing Solution Recommendations\n\n"
            if solutions:
                for i, solution in enumerate(solutions, 1):
                    result += f"""
### {i}. {solution['name']}
- **Rating**: {solution['rating']}
- **Core Features**: {solution['features']}
- **Pros**: {solution['pros']}
- **Cons**: {solution['cons']}
- **Website**: [{solution['name']}]({solution['website']})

"""
            else:
                result += "No relevant solution data available.\n"
            
            result += "\n*Note: This is demo data. The full version will search for the latest solutions in real-time.*"
    
    else:
        # Developer mode - show innovation opportunities
        if language == "zh":
            result = f"""
## 💡 创新机会分析

### 🎯 推荐行动
💎 **建议探索新方向**

### 📊 可行性评估
- **技术可行性**: 85% - 技术相对成熟
- **市场需求**: 78% - 存在明确需求
- **竞争程度**: 65% - 竞争适中，有差异化空间
- **法律风险**: 90% - 风险较低
- **成本控制**: 70% - 开发成本可控

### 🚀 下一步建议
1. 进行更详细的市场调研
2. 制作最小可行产品(MVP)
3. 寻找目标用户进行验证
4. 考虑技术实现方案

### ⚠️ 风险提醒
- 注意用户隐私保护
- 关注竞品动态
- 确保技术方案的可扩展性

*注：这是基于关键词的简化分析。完整版本将提供更深入的市场和技术分析。*
"""
        else:
            result = f"""
## 💡 Innovation Opportunity Analysis

### 🎯 Recommended Action
💎 **Suggest Exploring New Direction**

### 📊 Feasibility Assessment
- **Technical Feasibility**: 85% - Technology is relatively mature
- **Market Demand**: 78% - Clear demand exists
- **Competition Level**: 65% - Moderate competition with differentiation opportunities
- **Legal Risk**: 90% - Low risk
- **Cost Control**: 70% - Development costs are manageable

### 🚀 Next Steps
1. Conduct more detailed market research
2. Create a Minimum Viable Product (MVP)
3. Find target users for validation
4. Consider technical implementation solutions

### ⚠️ Risk Warning
- Pay attention to user privacy protection
- Monitor competitor dynamics
- Ensure scalability of technical solutions

*Note: This is a simplified analysis based on keywords. The full version will provide deeper market and technical analysis.*
"""
    
    return result

def generate_visualization(analysis: Dict[str, Any], language: str) -> str:
    """Generate visualization description"""
    
    if language == "zh":
        return """
## 📊 可视化分析

### 🎯 可行性雷达图
```
技术可行性: ████████░░ 85%
市场需求:   ███████░░░ 78%
竞争程度:   ██████░░░░ 65%
法律风险:   █████████░ 90%
成本控制:   ███████░░░ 70%
市场接受度: ████████░░ 82%
```

### 📈 趋势分析
- 该领域在过去12个月内搜索量增长 **23%**
- 相关产品发布数量增加 **15%**
- 用户满意度平均分: **4.2/5**

*注：可视化功能正在开发中。完整版本将提供交互式图表和更详细的数据分析。*
"""
    else:
        return """
## 📊 Visualization Analysis

### 🎯 Feasibility Radar Chart
```
Technical Feasibility: ████████░░ 85%
Market Demand:         ███████░░░ 78%
Competition Level:     ██████░░░░ 65%
Legal Risk:           █████████░ 90%
Cost Control:         ███████░░░ 70%
Market Acceptance:    ████████░░ 82%
```

### 📈 Trend Analysis
- Search volume in this field has grown **23%** in the past 12 months
- Number of related product releases increased by **15%**
- Average user satisfaction score: **4.2/5**

*Note: Visualization features are under development. The full version will provide interactive charts and more detailed data analysis.*
"""

def create_international_interface():
    """Create international Gradio interface"""

    css = """
    .gradio-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .main-header {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    .language-selector {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
    }
    .demo-notice {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        color: #1565c0;
    }
    .provider-info {
        background: #f5f5f5;
        border-radius: 8px;
        padding: 10px;
        margin: 10px 0;
        font-size: 0.9em;
    }
    """

    with gr.Blocks(css=css, title="InsightPulse - Demand Analysis Platform", theme=gr.themes.Soft()) as app:

        # Language state
        current_language = gr.State("en")  # Default to English for HF

        # Top language selector
        with gr.Row():
            with gr.Column(scale=4):
                pass  # Empty space
            with gr.Column(scale=1):
                language_btn = gr.Button("🌐 中文", size="sm", variant="secondary")

        # Header
        header_html = gr.HTML("""
        <div class="main-header">
            <h1>🔍 InsightPulse</h1>
            <p>Demand Exploration & Feasibility Analysis Platform</p>
            <p><em>Intelligently analyze your requirements, discover solutions, evaluate opportunities</em></p>
        </div>
        """)

        # Demo notice
        demo_notice = gr.HTML("""
        <div class="demo-notice">
            <h4>📢 Demo Notice</h4>
            <p>This is a demo version using simulated data to showcase platform features. The full version will integrate large language models for deep analysis.</p>
        </div>
        """)

        # Main interface
        with gr.Row():
            with gr.Column(scale=2):
                # Mode selector
                mode = gr.Radio(
                    choices=[
                        ("🔍 User Mode - Find existing solutions", "user"),
                        ("💡 Developer Mode - Explore innovation opportunities", "developer")
                    ],
                    value="user",
                    label="Mode",
                    info="Choose the appropriate mode for optimal analysis results"
                )

                # Input section
                user_input = gr.Textbox(
                    label="Requirement Description",
                    placeholder="Please describe your requirements or ideas in detail...\n\nExamples:\n- Recommend a good note-taking app\n- Develop an AI-driven code review tool",
                    lines=4,
                    max_lines=8
                )

            with gr.Column(scale=1):
                # AI Model selection
                provider_dropdown = gr.Dropdown(
                    choices=get_available_providers(),
                    value="demo",
                    label="AI Model Provider",
                    info="Select your preferred AI provider"
                )

                model_dropdown = gr.Dropdown(
                    choices=["demo-analyzer"],
                    value="demo-analyzer",
                    label="Select Model",
                    info="Choose the specific model to use"
                )

                # Provider info
                provider_info = gr.HTML("""
                <div class="provider-info">
                    <strong>Demo Mode</strong><br>
                    Uses keyword analysis for demonstration.<br>
                    Add API keys to enable real AI analysis.
                </div>
                """)

        # Action buttons
        with gr.Row():
            submit_btn = gr.Button("🚀 Start Analysis", variant="primary", size="lg")
            clear_btn = gr.Button("🗑️ Clear", size="lg")

        # Examples
        with gr.Row():
            gr.Examples(
                examples=[
                    ["Recommend a good note-taking app", "user"],
                    ["Find an online collaboration tool", "user"],
                    ["Develop an AI-driven code review tool", "developer"],
                    ["Create an intelligent customer service chatbot", "developer"],
                    ["Build a blockchain voting system", "developer"]
                ],
                inputs=[user_input, mode],
                label="Examples"
            )

        # Results section
        with gr.Row():
            with gr.Column():
                analysis_output = gr.Markdown(
                    label="📋 Analysis Results",
                    value="Click 'Start Analysis' to see results..."
                )
            with gr.Column():
                recommendations_output = gr.Markdown(
                    label="💡 Recommendations",
                    value="Analysis results will appear here..."
                )

        with gr.Row():
            visualization_output = gr.Markdown(
                label="📊 Visualization Analysis",
                value="Visualization will be generated after analysis..."
            )

        # Language switching function
        def switch_language(current_lang):
            new_lang = "zh" if current_lang == "en" else "en"
            ui_texts = update_interface_language(new_lang)

            # Update button text
            btn_text = "🌐 English" if new_lang == "zh" else "🌐 中文"

            # Update header
            if new_lang == "zh":
                header = """
                <div class="main-header">
                    <h1>🔍 逛逛 (InsightPulse)</h1>
                    <p>需求探索与可行性分析平台</p>
                    <p><em>智能分析您的需求，发现现有解决方案，评估创新机会</em></p>
                </div>
                """
                notice = """
                <div class="demo-notice">
                    <h4>📢 演示说明</h4>
                    <p>这是演示版本，使用模拟数据展示平台功能。完整版本将集成大语言模型进行深度分析。</p>
                </div>
                """
            else:
                header = """
                <div class="main-header">
                    <h1>🔍 InsightPulse</h1>
                    <p>Demand Exploration & Feasibility Analysis Platform</p>
                    <p><em>Intelligently analyze your requirements, discover solutions, evaluate opportunities</em></p>
                </div>
                """
                notice = """
                <div class="demo-notice">
                    <h4>📢 Demo Notice</h4>
                    <p>This is a demo version using simulated data to showcase platform features. The full version will integrate large language models for deep analysis.</p>
                </div>
                """

            return (
                new_lang,  # Update language state
                btn_text,  # Update button text
                header,    # Update header
                notice,    # Update demo notice
                gr.update(
                    choices=[
                        (ui_texts["user_mode"], "user"),
                        (ui_texts["developer_mode"], "developer")
                    ],
                    label=ui_texts["mode_label"]
                ),  # Update mode radio
                gr.update(
                    label=ui_texts["input_label"],
                    placeholder=ui_texts["input_placeholder"]
                ),  # Update input textbox
                gr.update(label=ui_texts["provider_label"]),  # Update provider dropdown
                gr.update(label=ui_texts["model_label"]),     # Update model dropdown
                gr.update(value=ui_texts["submit_button"]),   # Update submit button
                gr.update(value=ui_texts["clear_button"])     # Update clear button
            )

        # Provider change function
        def update_models(provider):
            models = get_models_for_provider(provider)
            model_choices = [(model, model) for model in models]

            # Update provider info
            if provider == "demo":
                info_html = """
                <div class="provider-info">
                    <strong>🎭 Demo Mode</strong><br>
                    Uses keyword analysis for demonstration.<br>
                    <em>Add API keys to enable real AI analysis.</em>
                </div>
                """
            else:
                provider_info = MODEL_PROVIDERS.get(provider, {})
                provider_name = provider_info.get("name", provider)
                env_key = provider_info.get("env_key", "")
                description = provider_info.get("description", "")

                # Check if API key is available
                api_available = "✅" if os.getenv(env_key) else "❌"

                info_html = f"""
                <div class="provider-info">
                    <strong>{api_available} {provider_name}</strong><br>
                    <em>{description}</em><br>
                    <small>Environment: {env_key}</small><br>
                    <small>Models: {len(models)} available</small>
                </div>
                """

            return gr.update(choices=model_choices, value=models[0] if models else None), info_html

        # Event handlers
        language_btn.click(
            fn=switch_language,
            inputs=[current_language],
            outputs=[
                current_language, language_btn, header_html, demo_notice,
                mode, user_input, provider_dropdown, model_dropdown,
                submit_btn, clear_btn
            ]
        )

        provider_dropdown.change(
            fn=update_models,
            inputs=[provider_dropdown],
            outputs=[model_dropdown, provider_info]
        )

        submit_btn.click(
            fn=analyze_demand_international,
            inputs=[user_input, mode, current_language, provider_dropdown, model_dropdown],
            outputs=[analysis_output, recommendations_output, visualization_output]
        )

        clear_btn.click(
            fn=lambda: ("", "", "", ""),
            outputs=[user_input, analysis_output, recommendations_output, visualization_output]
        )

        # Footer
        gr.HTML(f"""
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <p><strong>🚀 Powered by</strong></p>
            <p>Gradio • Python • Hugging Face Spaces</p>
            <p><em>HF Hackathon Project</em></p>
            <p>Version: {config.app.app_version}</p>
        </div>
        """)

    return app

def main():
    """Main function for international app"""
    logger.info("Starting InsightPulse International Application...")

    app = create_international_interface()

    # Launch configuration
    launch_kwargs = {
        "server_name": "0.0.0.0",
        "server_port": 7860,
        "share": False,
        "show_error": True
    }

    logger.info("Launching international application on port 7860")
    app.launch(**launch_kwargs)

if __name__ == "__main__":
    main()
