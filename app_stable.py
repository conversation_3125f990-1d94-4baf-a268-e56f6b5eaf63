"""
InsightPulse - 稳定版本
去除所有可能导致Segmentation fault的复杂组件
"""

import gradio as gr
import time
from typing import Tuple

def analyze_demand(user_input: str, mode: str, language: str) -> Tuple[str, str]:
    """简化的需求分析函数"""
    
    if not user_input.strip():
        error_msg = "请输入您的需求描述" if language == "zh" else "Please enter your requirement description"
        return error_msg, ""
    
    # 模拟处理时间
    time.sleep(1)
    
    if language == "zh":
        analysis_result = f"""
## 📋 AI分析结果

**用户需求**: {user_input}

**需求分析**:
- 🎯 **核心需求**: 寻找高效的笔记管理解决方案
- 📱 **使用场景**: 日常记录、知识管理、信息整理
- 👥 **目标用户**: 个人用户或小团队
- ⭐ **关键特性**: 同步、搜索、组织、分享

**市场洞察**:
- 📊 笔记软件市场规模持续增长
- 🔥 用户更偏好简洁易用的界面
- 🌐 跨平台同步是必备功能
- 🤖 AI辅助功能成为新趋势

**分析置信度**: 85%
"""
        
        recommendations = f"""
## 💡 推荐方案

### 🔍 现有解决方案推荐

**1. 印象笔记 (Evernote)**
- ⭐ 评分: 4.5/5
- 💪 优势: 功能全面、OCR识别、网页剪藏
- 💰 价格: 免费版 + 付费版
- 🎯 适合: 重度笔记用户

**2. Notion**
- ⭐ 评分: 4.7/5  
- 💪 优势: 块编辑器、数据库、协作
- 💰 价格: 免费版 + 付费版
- 🎯 适合: 团队协作、项目管理

**3. Obsidian**
- ⭐ 评分: 4.6/5
- 💪 优势: 双向链接、本地存储、插件丰富
- 💰 价格: 个人免费
- 🎯 适合: 知识管理、学术研究

**4. 飞书文档**
- ⭐ 评分: 4.4/5
- 💪 优势: 协作友好、中文优化
- 💰 价格: 免费版 + 付费版
- 🎯 适合: 中文用户、团队协作

### 📊 对比总结
- **最全面**: 印象笔记
- **最灵活**: Notion  
- **最专业**: Obsidian
- **最本土**: 飞书文档

*基于AI分析的个性化推荐*
"""
    else:
        analysis_result = f"""
## 📋 AI Analysis Results

**User Requirement**: {user_input}

**Requirement Analysis**:
- 🎯 **Core Need**: Efficient note-taking and management solution
- 📱 **Use Cases**: Daily recording, knowledge management, information organization
- 👥 **Target Users**: Individual users or small teams
- ⭐ **Key Features**: Sync, search, organization, sharing

**Market Insights**:
- 📊 Note-taking software market continues to grow
- 🔥 Users prefer clean and intuitive interfaces
- 🌐 Cross-platform sync is essential
- 🤖 AI-assisted features are trending

**Confidence Score**: 85%
"""
        
        recommendations = f"""
## 💡 Recommendations

### 🔍 Existing Solution Recommendations

**1. Evernote**
- ⭐ Rating: 4.5/5
- 💪 Strengths: Comprehensive features, OCR, web clipper
- 💰 Pricing: Free + Premium plans
- 🎯 Best for: Heavy note-takers

**2. Notion**
- ⭐ Rating: 4.7/5
- 💪 Strengths: Block editor, databases, collaboration
- 💰 Pricing: Free + Paid plans
- 🎯 Best for: Team collaboration, project management

**3. Obsidian**
- ⭐ Rating: 4.6/5
- 💪 Strengths: Bidirectional links, local storage, rich plugins
- 💰 Pricing: Free for personal use
- 🎯 Best for: Knowledge management, academic research

**4. OneNote**
- ⭐ Rating: 4.3/5
- 💪 Strengths: Microsoft integration, free, handwriting support
- 💰 Pricing: Free
- 🎯 Best for: Microsoft ecosystem users

### 📊 Comparison Summary
- **Most Comprehensive**: Evernote
- **Most Flexible**: Notion
- **Most Professional**: Obsidian
- **Most Integrated**: OneNote

*Personalized recommendations based on AI analysis*
"""
    
    return analysis_result, recommendations

def create_stable_interface():
    """创建稳定的Gradio界面"""
    
    # 使用最基础的Gradio组件，避免复杂功能
    with gr.Blocks(
        title="InsightPulse",
        theme=gr.themes.Default()
    ) as app:
        
        # 简单的标题
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin-bottom: 20px;">
            <h1>🔍 InsightPulse</h1>
            <p>AI-Powered Demand Exploration & Feasibility Analysis</p>
        </div>
        """)
        
        # 语言状态
        current_language = gr.State("en")
        
        # 语言切换按钮
        with gr.Row():
            language_btn = gr.Button("🌐 中文", size="sm")
        
        # 主要输入区域
        with gr.Row():
            with gr.Column():
                # 模式选择 - 使用简单的Radio
                mode = gr.Radio(
                    choices=[
                        ("🔍 User Mode - Find existing solutions", "user"),
                        ("💡 Developer Mode - Explore opportunities", "developer")
                    ],
                    value="user",
                    label="Analysis Mode"
                )
                
                # 用户输入
                user_input = gr.Textbox(
                    label="Requirement Description",
                    placeholder="Please describe your requirements...\n\nExample: Recommend a good note-taking app",
                    lines=4
                )
                
                # 提交按钮
                submit_btn = gr.Button("🚀 Start Analysis", variant="primary", size="lg")
                clear_btn = gr.Button("🗑️ Clear", size="lg")
        
        # 简单的示例
        gr.Examples(
            examples=[
                ["Recommend a good note-taking app", "user"],
                ["Find a project management tool", "user"],
                ["Develop a new productivity app", "developer"]
            ],
            inputs=[user_input, mode],
            label="Examples"
        )
        
        # 结果显示 - 使用简单的Markdown组件
        with gr.Row():
            with gr.Column():
                analysis_output = gr.Markdown(
                    value="Click 'Start Analysis' to see AI analysis results...",
                    label="📋 AI Analysis"
                )
            with gr.Column():
                recommendations_output = gr.Markdown(
                    value="Recommendations will appear here...",
                    label="💡 Recommendations"
                )
        
        # 简单的事件处理
        def switch_language(current_lang):
            new_lang = "zh" if current_lang == "en" else "en"
            btn_text = "🌐 English" if new_lang == "zh" else "🌐 中文"
            return new_lang, btn_text
        
        def clear_all():
            return "", "", ""
        
        # 绑定事件
        language_btn.click(
            fn=switch_language,
            inputs=[current_language],
            outputs=[current_language, language_btn]
        )
        
        submit_btn.click(
            fn=analyze_demand,
            inputs=[user_input, mode, current_language],
            outputs=[analysis_output, recommendations_output]
        )
        
        clear_btn.click(
            fn=clear_all,
            outputs=[user_input, analysis_output, recommendations_output]
        )
        
        # 简单的页脚
        gr.HTML("""
        <div style="text-align: center; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <p><strong>🚀 InsightPulse</strong> - AI-Powered Analysis Platform</p>
            <p><em>Demo Mode - All features available without API keys</em></p>
        </div>
        """)
    
    return app

def main():
    """主函数"""
    print("🚀 Starting InsightPulse (Stable Version)...")
    
    app = create_stable_interface()
    
    # 使用最基础的启动配置
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True,
        quiet=False
    )

if __name__ == "__main__":
    main()
