{"app": {"title": "逛逛 - 需求探索与可行性分析平台", "subtitle": "连接需求与解决方案，赋能创新决策", "description": "智能分析您的需求，发现现有解决方案，评估创新机会"}, "ui": {"language_selector": "语言选择", "mode_selector": "使用模式", "input_placeholder": "请描述您的需求或想法...", "submit_button": "开始分析", "clear_button": "清空", "export_button": "导出报告", "loading": "正在分析中，请稍候...", "error": "分析过程中出现错误，请重试", "no_results": "未找到相关结果"}, "modes": {"user": {"name": "普通用户模式", "description": "寻找满足特定需求的现有解决方案", "icon": "🔍"}, "developer": {"name": "开发者模式", "description": "探索创新机会，评估项目可行性", "icon": "💡"}}, "analysis": {"demand_parsing": {"title": "需求解析", "intent": "用户意图", "category": "需求领域", "keywords": "关键词", "constraints": "约束条件"}, "ecosystem_scan": {"title": "生态扫描", "existing_solutions": "现有解决方案", "top_recommendations": "精选推荐", "competitor_analysis": "竞品分析", "market_overview": "市场概览"}, "innovation_radar": {"title": "创新雷达", "feasibility": "技术可行性", "market_demand": "市场需求", "competition": "竞争程度", "legal_risk": "法律风险", "cost_control": "成本控制", "market_acceptance": "市场接受度"}, "case_analysis": {"title": "历史案例分析", "similar_projects": "相似项目", "success_rate": "成功率", "failure_reasons": "失败原因", "key_factors": "关键要素", "lessons_learned": "经验教训"}, "decision_engine": {"title": "决策建议", "recommendation": "推荐行动", "reasoning": "分析理由", "next_steps": "下一步行动", "risk_warning": "风险提醒"}}, "recommendations": {"abandon": {"icon": "🚫", "title": "建议放弃", "description": "竞品饱和度高且无明显差异化机会"}, "proceed": {"icon": "💎", "title": "推荐新方向", "description": "发现蓝海机会或现有方案的改进点"}, "high_risk": {"icon": "⚠️", "title": "高风险警告", "description": "存在显著的技术、法律或市场风险"}, "optimize": {"icon": "🔧", "title": "优化建议", "description": "现有方案可通过改进获得竞争优势"}}, "results": {"solution_card": {"rating": "评分", "features": "核心功能", "pros": "优势", "cons": "劣势", "pricing": "定价", "website": "官网", "last_updated": "最近更新"}, "radar_chart": {"title": "可行性雷达图", "score": "得分"}, "export": {"pdf_title": "需求分析报告", "generated_at": "生成时间", "user_query": "用户需求", "analysis_summary": "分析摘要"}}, "errors": {"api_error": "API调用失败，请稍后重试", "network_error": "网络连接错误", "timeout_error": "请求超时，请重试", "invalid_input": "输入内容无效，请检查后重试", "service_unavailable": "服务暂时不可用", "rate_limit": "请求过于频繁，请稍后再试"}, "footer": {"powered_by": "技术支持", "version": "版本", "contact": "联系我们", "privacy": "隐私政策", "terms": "使用条款"}, "examples": {"user_mode": ["有什么好用的笔记软件", "推荐一个在线协作工具", "寻找代码编辑器", "需要一个项目管理工具"], "developer_mode": ["开发一个AI驱动的代码审查工具", "创建智能客服聊天机器人", "构建区块链投票系统", "设计AR购物应用"]}, "tips": {"input_tips": "💡 提示：描述越详细，分析结果越准确", "mode_tips": "选择合适的模式以获得最佳分析结果", "language_tips": "支持中英文切换，内容将自动适配"}}