"""
Main application file for InsightPulse platform.
Gradio-based web interface for demand exploration and feasibility analysis.
"""

import gradio as gr
import os
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
import json

# Import our modules
from utils.config import get_config
from utils.logger import get_logger
from i18n.loader import get_i18n, t, set_language
from llm.base import LLMManager
from llm.openai_client import create_openai_client
from core.demand_parser import DemandPars<PERSON>, UserMode

# Initialize components
config = get_config()
logger = get_logger("MainApp")
i18n = get_i18n()

# Initialize LLM Manager
llm_manager = LLMManager()

# Add available LLM clients
if config.llm.openai_api_key:
    openai_client = create_openai_client(config.llm.openai_api_key)
    llm_manager.add_client("openai", openai_client, is_primary=True)
    logger.info("OpenAI client initialized")

# Initialize core modules
demand_parser = DemandParser(llm_manager)

def analyze_demand(user_input: str, mode: str, language: str) -> Tuple[str, str, str]:
    """
    Analyze user demand and return results
    
    Args:
        user_input: User's input text
        mode: Selected mode (user/developer)
        language: Selected language
        
    Returns:
        Tuple of (analysis_result, recommendations, visualization)
    """
    try:
        logger.info(f"Analyzing demand: {user_input[:50]}... in {language} mode: {mode}")
        
        # Set language
        set_language(language)
        
        # Parse demand
        analysis = demand_parser.parse_demand(user_input, language)
        
        # Format analysis result
        analysis_result = format_analysis_result(analysis, language)
        
        # Generate recommendations (placeholder for now)
        recommendations = generate_recommendations(analysis, language)
        
        # Generate visualization (placeholder for now)
        visualization = "📊 Visualization will be implemented in next phase"
        
        return analysis_result, recommendations, visualization
        
    except Exception as e:
        logger.error(f"Error analyzing demand: {e}")
        error_msg = t("errors.api_error", language)
        return error_msg, "", ""

def format_analysis_result(analysis, language: str) -> str:
    """Format analysis result for display"""
    result = f"""
## {t("analysis.demand_parsing.title", language)}

**{t("analysis.demand_parsing.intent", language)}**: {analysis.user_intent}

**{t("analysis.demand_parsing.category", language)}**: {analysis.category.value}

**{t("analysis.demand_parsing.keywords", language)}**: {', '.join(analysis.keywords)}

**{t("ui.mode_selector", language)}**: {analysis.user_mode.value}

**置信度**: {analysis.confidence_score:.2f}
"""
    return result

def generate_recommendations(analysis, language: str) -> str:
    """Generate recommendations based on analysis"""
    if analysis.user_mode == UserMode.USER:
        return f"""
## {t("analysis.ecosystem_scan.title", language)}

{t("analysis.ecosystem_scan.existing_solutions", language)}:
- 解决方案 1: 示例应用 A
- 解决方案 2: 示例应用 B  
- 解决方案 3: 示例应用 C

*注：实际的生态扫描功能将在下一阶段实现*
"""
    else:
        return f"""
## {t("analysis.innovation_radar.title", language)}

{t("analysis.decision_engine.recommendation", language)}:
💡 **建议探索新方向**

{t("analysis.decision_engine.reasoning", language)}:
- 市场需求较高
- 竞争程度适中
- 技术可行性良好

*注：详细的可行性分析将在下一阶段实现*
"""

def create_interface():
    """Create Gradio interface"""
    
    # Custom CSS
    css = """
    .gradio-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .main-header {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    .mode-selector {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
    }
    """
    
    with gr.Blocks(css=css, title="逛逛 - InsightPulse", theme=gr.themes.Soft()) as app:
        
        # Header
        gr.HTML("""
        <div class="main-header">
            <h1>🔍 逛逛 (InsightPulse)</h1>
            <p>需求探索与可行性分析平台</p>
            <p><em>Demand Exploration & Feasibility Analysis Platform</em></p>
        </div>
        """)
        
        # Language selector
        with gr.Row():
            language = gr.Dropdown(
                choices=[("中文", "zh"), ("English", "en")],
                value="zh",
                label="语言 / Language",
                interactive=True
            )
        
        # Mode selector
        with gr.Row():
            mode = gr.Radio(
                choices=[
                    ("🔍 普通用户模式 - 寻找现有解决方案", "user"),
                    ("💡 开发者模式 - 探索创新机会", "developer")
                ],
                value="user",
                label="使用模式 / Mode",
                info="选择合适的模式以获得最佳分析结果"
            )
        
        # Input section
        with gr.Row():
            with gr.Column():
                user_input = gr.Textbox(
                    label="需求描述 / Requirement Description",
                    placeholder="请详细描述您的需求或想法...\nPlease describe your requirements or ideas in detail...",
                    lines=4,
                    max_lines=8
                )
                
                with gr.Row():
                    submit_btn = gr.Button("🚀 开始分析 / Start Analysis", variant="primary")
                    clear_btn = gr.Button("🗑️ 清空 / Clear")
        
        # Examples
        with gr.Row():
            gr.Examples(
                examples=[
                    ["有什么好用的笔记软件", "user", "zh"],
                    ["推荐一个在线协作工具", "user", "zh"],
                    ["开发一个AI驱动的代码审查工具", "developer", "zh"],
                    ["What are some good note-taking apps", "user", "en"],
                    ["Develop an AI-driven code review tool", "developer", "en"]
                ],
                inputs=[user_input, mode, language],
                label="示例 / Examples"
            )
        
        # Results section
        with gr.Row():
            with gr.Column():
                analysis_output = gr.Markdown(
                    label="📋 需求分析结果 / Analysis Results",
                    value="点击'开始分析'查看结果..."
                )
            
            with gr.Column():
                recommendations_output = gr.Markdown(
                    label="💡 推荐建议 / Recommendations", 
                    value="分析完成后将显示推荐..."
                )
        
        # Visualization section (placeholder)
        with gr.Row():
            visualization_output = gr.Markdown(
                label="📊 可视化分析 / Visualization",
                value="可视化功能开发中..."
            )
        
        # Event handlers
        submit_btn.click(
            fn=analyze_demand,
            inputs=[user_input, mode, language],
            outputs=[analysis_output, recommendations_output, visualization_output]
        )
        
        clear_btn.click(
            fn=lambda: ("", "", "", ""),
            outputs=[user_input, analysis_output, recommendations_output, visualization_output]
        )
        
        # Footer
        gr.HTML("""
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <p><strong>🚀 技术支持 / Powered by</strong></p>
            <p>Gradio • OpenAI • Hugging Face Spaces</p>
            <p><em>HF黑客松参赛作品 / HF Hackathon Project</em></p>
            <p>版本 / Version: {version}</p>
        </div>
        """.format(version=config.app.app_version))
    
    return app

def main():
    """Main function to launch the application"""
    logger.info("Starting InsightPulse application...")
    
    # Validate configuration
    config_status = config.validate_configuration()
    if not config_status['valid']:
        logger.error(f"Configuration validation failed: {config_status['errors']}")
        return
    
    if config_status['warnings']:
        for warning in config_status['warnings']:
            logger.warning(warning)
    
    # Create and launch interface
    app = create_interface()
    
    # Launch configuration
    launch_kwargs = {
        "server_name": "0.0.0.0",
        "server_port": config.huggingface.gradio_server_port,
        "share": config.huggingface.gradio_share,
        "show_error": config.app.debug,
        "quiet": not config.app.debug
    }
    
    logger.info(f"Launching application on port {config.huggingface.gradio_server_port}")
    app.launch(**launch_kwargs)

if __name__ == "__main__":
    main()
