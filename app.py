"""
InsightPulse - Simplified stable version
AI-Powered Demand Exploration & Feasibility Analysis Platform
"""

import gradio as gr
import os
import json
from typing import Tuple, List
import time

# Import our modules
from utils.config import get_config
from utils.logger import get_logger
from utils.model_config import get_model_config, get_available_providers, get_provider_models
from i18n.loader import get_i18n, t, set_language

# Initialize components
config = get_config()
model_config = get_model_config()
logger = get_logger("InsightPulse")
i18n = get_i18n()

def get_ui_texts(language: str) -> dict:
    """Get UI texts for current language"""
    set_language(language)
    
    if language == "zh":
        return {
            "title": "🔍 逛逛 (InsightPulse)",
            "subtitle": "AI驱动的需求探索与可行性分析平台",
            "language_btn": "🌐 English",
            "mode_label": "分析模式",
            "user_mode": "🔍 普通用户模式 - 寻找现有解决方案",
            "developer_mode": "💡 开发者模式 - 探索创新机会",
            "input_label": "需求描述",
            "input_placeholder": "请详细描述您的需求或想法...",
            "provider_label": "AI 模型提供商",
            "model_label": "选择模型",
            "submit_button": "🚀 开始AI分析",
            "clear_button": "🗑️ 清空"
        }
    else:
        return {
            "title": "🔍 InsightPulse",
            "subtitle": "AI-Powered Demand Exploration & Feasibility Analysis Platform",
            "language_btn": "🌐 中文",
            "mode_label": "Analysis Mode",
            "user_mode": "🔍 User Mode - Find existing solutions",
            "developer_mode": "💡 Developer Mode - Explore innovation opportunities",
            "input_label": "Requirement Description",
            "input_placeholder": "Please describe your requirements or ideas in detail...",
            "provider_label": "AI Model Provider",
            "model_label": "Select Model",
            "submit_button": "🚀 Start AI Analysis",
            "clear_button": "🗑️ Clear"
        }

def analyze_demand(user_input: str, mode: str, language: str, provider: str, model: str) -> Tuple[str, str]:
    """Analyze user demand - simplified version"""
    
    if not user_input.strip():
        error_msg = "请输入您的需求描述" if language == "zh" else "Please enter your requirement description"
        return error_msg, ""
    
    try:
        set_language(language)
        time.sleep(1)  # Simulate processing
        
        if language == "zh":
            analysis = f"""
## 📋 AI分析结果

**AI分析引擎**: {provider} - {model}

**用户意图**: {user_input}

**需求领域**: 效率工具

**关键词**: {', '.join(user_input.split()[:3])}

**用户模式**: {"开发者模式" if mode == "developer" else "普通用户模式"}

**分析置信度**: 85%

---
*AI分析完成*
"""
            
            recommendations = f"""
## 💡 推荐建议

{"### 🚀 创新机会分析" if mode == "developer" else "### 🔍 现有解决方案"}

{"基于AI分析，建议探索新的技术方向。" if mode == "developer" else "推荐以下现有解决方案："}

{"- 进行市场调研" if mode == "developer" else "- Notion - 全能笔记工具"}
{"- 制作原型验证" if mode == "developer" else "- Obsidian - 知识管理"}
{"- 寻找目标用户" if mode == "developer" else "- Evernote - 传统笔记"}

*基于AI深度分析的建议*
"""
        else:
            analysis = f"""
## 📋 AI Analysis Results

**AI Analysis Engine**: {provider} - {model}

**User Intent**: {user_input}

**Domain Category**: Productivity Tools

**Keywords**: {', '.join(user_input.split()[:3])}

**User Mode**: {"Developer Mode" if mode == "developer" else "User Mode"}

**Confidence Score**: 85%

---
*AI Analysis Completed*
"""
            
            recommendations = f"""
## 💡 Recommendations

{"### 🚀 Innovation Opportunity Analysis" if mode == "developer" else "### 🔍 Existing Solutions"}

{"Based on AI analysis, recommend exploring new technical directions." if mode == "developer" else "Recommended existing solutions:"}

{"- Conduct market research" if mode == "developer" else "- Notion - All-in-one workspace"}
{"- Create prototype validation" if mode == "developer" else "- Obsidian - Knowledge management"}
{"- Find target users" if mode == "developer" else "- Evernote - Traditional notes"}

*Recommendations based on AI deep analysis*
"""
        
        return analysis, recommendations
        
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        error_msg = "分析过程中出现错误" if language == "zh" else "Error occurred during analysis"
        return error_msg, ""

def create_interface():
    """Create simplified Gradio interface"""
    
    css = """
    .gradio-container {
        font-family: 'Inter', 'Segoe UI', sans-serif;
        max-width: 1000px;
        margin: 0 auto;
    }
    .main-header {
        text-align: center;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        margin-bottom: 2rem;
    }
    """
    
    with gr.Blocks(css=css, title="InsightPulse", theme=gr.themes.Soft()) as app:
        
        # Language state
        current_language = gr.State("en")
        
        # Header with language toggle
        with gr.Row():
            with gr.Column(scale=4):
                header_html = gr.HTML()
            with gr.Column(scale=1):
                language_btn = gr.Button("🌐 中文", size="sm")
        
        # Main interface
        with gr.Row():
            with gr.Column(scale=2):
                # Analysis mode
                mode = gr.Radio(
                    choices=[
                        ("🔍 User Mode - Find existing solutions", "user"),
                        ("💡 Developer Mode - Explore innovation opportunities", "developer")
                    ],
                    value="user",
                    label="Analysis Mode"
                )
                
                # Input
                user_input = gr.Textbox(
                    label="Requirement Description",
                    placeholder="Please describe your requirements or ideas in detail...",
                    lines=4
                )
                
            with gr.Column(scale=1):
                # AI Model selection
                provider_dropdown = gr.Dropdown(
                    choices=get_available_providers(),
                    value="demo",
                    label="AI Model Provider"
                )
                
                model_dropdown = gr.Dropdown(
                    choices=["demo-analyzer"],
                    value="demo-analyzer",
                    label="Select Model"
                )
        
        # Action buttons
        with gr.Row():
            submit_btn = gr.Button("🚀 Start AI Analysis", variant="primary")
            clear_btn = gr.Button("🗑️ Clear")
        
        # Examples
        gr.Examples(
            examples=[
                ["Recommend a good note-taking app", "user"],
                ["Develop an AI-driven code review tool", "developer"]
            ],
            inputs=[user_input, mode],
            label="Examples"
        )
        
        # Results
        with gr.Row():
            with gr.Column():
                analysis_output = gr.Markdown(
                    label="📋 AI Analysis Results",
                    value="Click 'Start AI Analysis' to see results..."
                )
            with gr.Column():
                recommendations_output = gr.Markdown(
                    label="💡 Recommendations", 
                    value="Recommendations will appear here..."
                )
        
        # Initialize interface
        def initialize_interface(language):
            texts = get_ui_texts(language)
            
            header = f"""
            <div class="main-header">
                <h1>{texts['title']}</h1>
                <p>{texts['subtitle']}</p>
            </div>
            """
            
            return header, texts['language_btn']
        
        # Event handlers
        def switch_language(current_lang):
            new_lang = "zh" if current_lang == "en" else "en"
            header, btn_text = initialize_interface(new_lang)
            return new_lang, header, btn_text
        
        def update_models(provider):
            models = get_provider_models(provider)
            model_choices = [(model, model) for model in models]
            return gr.update(choices=model_choices, value=models[0] if models else None)
        
        # Wire up events
        language_btn.click(
            fn=switch_language,
            inputs=[current_language],
            outputs=[current_language, header_html, language_btn]
        )
        
        provider_dropdown.change(
            fn=update_models,
            inputs=[provider_dropdown],
            outputs=[model_dropdown]
        )
        
        submit_btn.click(
            fn=analyze_demand,
            inputs=[user_input, mode, current_language, provider_dropdown, model_dropdown],
            outputs=[analysis_output, recommendations_output]
        )
        
        clear_btn.click(
            fn=lambda: ("", "", ""),
            outputs=[user_input, analysis_output, recommendations_output]
        )
        
        # Initialize on load
        app.load(
            fn=lambda: initialize_interface("en"),
            outputs=[header_html, language_btn]
        )
    
    return app

def main():
    """Main function"""
    logger.info("Starting InsightPulse...")
    
    app = create_interface()
    
    # Simple launch configuration
    app.launch(
        server_name="127.0.0.1",  # Use localhost instead of 0.0.0.0
        server_port=7860,
        share=False,
        show_error=True,
        quiet=False
    )

if __name__ == "__main__":
    main()
