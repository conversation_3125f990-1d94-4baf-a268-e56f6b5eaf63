"""
InsightPulse - AI-Powered Demand Exploration & Feasibility Analysis Platform
Modern Gradio interface with configurable models, real search integration, and improved UX.
"""

import gradio as gr
import os
import asyncio
from typing import Optional, Tuple, Dict, Any, List
import time
import json

# Import our modules
from utils.config import get_config
from utils.logger import get_logger
from utils.model_config import get_model_config, get_available_providers, get_provider_models, get_provider_status
from i18n.loader import get_i18n, t, set_language
from core.search_engine import create_search_engine, create_competitor_search_query, SearchQuery
from core.ai_processor import AIProcessor, AnalysisRequest
from llm.base import LLMManager

# Initialize components
config = get_config()
model_config = get_model_config()
logger = get_logger("AppV2")
i18n = get_i18n()

# Initialize search engine
search_engine = create_search_engine()

# Initialize LLM Manager
llm_manager = LLMManager()

def get_ui_texts(language: str) -> Dict[str, str]:
    """Get UI texts for current language"""
    set_language(language)

    if language == "zh":
        return {
            "title": "🔍 逛逛 (InsightPulse)",
            "subtitle": "AI驱动的需求探索与可行性分析平台",
            "description": "智能分析您的需求，发现现有解决方案，评估创新机会",
            "language_btn": "🌐 English",
            "mode_label": "分析模式",
            "user_mode": "🔍 普通用户模式 - 寻找现有解决方案",
            "developer_mode": "💡 开发者模式 - 探索创新机会",
            "input_label": "需求描述",
            "input_placeholder": "请详细描述您的需求或想法...\n\n示例：\n- 推荐一个好用的笔记软件\n- 开发一个AI驱动的代码审查工具",
            "provider_label": "AI 模型提供商",
            "model_label": "选择模型",
            "search_label": "搜索设置",
            "submit_button": "🚀 开始AI分析",
            "clear_button": "🗑️ 清空",
            "analysis_tab": "📋 AI分析结果",
            "search_tab": "🔍 搜索结果",
            "visualization_tab": "📊 数据可视化"
        }
    else:
        return {
            "title": "🔍 InsightPulse",
            "subtitle": "AI-Powered Demand Exploration & Feasibility Analysis Platform",
            "description": "Intelligently analyze your requirements, discover solutions, evaluate opportunities",
            "language_btn": "🌐 中文",
            "mode_label": "Analysis Mode",
            "user_mode": "🔍 User Mode - Find existing solutions",
            "developer_mode": "💡 Developer Mode - Explore innovation opportunities",
            "input_label": "Requirement Description",
            "input_placeholder": "Please describe your requirements or ideas in detail...\n\nExamples:\n- Recommend a good note-taking app\n- Develop an AI-driven code review tool",
            "provider_label": "AI Model Provider",
            "model_label": "Select Model",
            "search_label": "Search Settings",
            "submit_button": "🚀 Start AI Analysis",
            "clear_button": "🗑️ Clear",
            "analysis_tab": "📋 AI Analysis Results",
            "search_tab": "🔍 Search Results",
            "visualization_tab": "📊 Data Visualization"
        }

async def perform_analysis(
    user_input: str,
    mode: str,
    language: str,
    provider: str,
    model: str,
    enable_search: bool,
    search_sources: List[str],
    max_search_results: int
) -> Tuple[str, str, str]:
    """Perform comprehensive analysis with AI and search"""
    
    if not user_input.strip():
        error_msg = "请输入您的需求描述" if language == "zh" else "Please enter your requirement description"
        return error_msg, "", ""
    
    try:
        set_language(language)
        
        # Create analysis request
        request = AnalysisRequest(
            user_input=user_input,
            language=language,
            mode=mode,
            provider=provider,
            model=model
        )
        
        # Perform AI analysis (simplified for demo)
        analysis_result = await simulate_ai_analysis(request)
        
        # Perform search if enabled
        search_result = ""
        if enable_search and search_sources:
            search_result = await perform_search_analysis(user_input, language, search_sources, max_search_results)
        
        # Generate visualization
        visualization_result = generate_visualization_summary(analysis_result, search_result, language)
        
        return analysis_result, search_result, visualization_result
        
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        error_msg = "分析过程中出现错误" if language == "zh" else "Error occurred during analysis"
        return error_msg, "", ""

async def simulate_ai_analysis(request: AnalysisRequest) -> str:
    """Simulate AI analysis (replace with real implementation)"""
    await asyncio.sleep(1)  # Simulate processing time
    
    if request.language == "zh":
        return f"""
## 📋 AI分析结果

**AI分析引擎**: {request.provider} - {request.model}

**用户意图**: {request.user_input}

**需求领域**: 效率工具

**关键词**: 笔记, 软件, 推荐

**用户模式**: {"开发者模式" if request.mode == "developer" else "普通用户模式"}

**分析置信度**: 85%

**处理时间**: 1.2秒

---
*AI深度分析完成 - 这是演示版本*
"""
    else:
        return f"""
## 📋 AI Analysis Results

**AI Analysis Engine**: {request.provider} - {request.model}

**User Intent**: {request.user_input}

**Domain Category**: Productivity Tools

**Keywords**: note, software, recommend

**User Mode**: {"Developer Mode" if request.mode == "developer" else "User Mode"}

**Confidence Score**: 85%

**Processing Time**: 1.2s

---
*AI Deep Analysis Completed - Demo Version*
"""

async def perform_search_analysis(user_input: str, language: str, sources: List[str], max_results: int) -> str:
    """Perform search analysis"""
    try:
        # Extract keywords for search
        keywords = user_input.split()[:3]  # Simple keyword extraction
        
        # Create search query
        search_query = SearchQuery(
            query=user_input,
            language=language,
            max_results=max_results,
            sources=sources
        )
        
        # Perform search
        search_response = await search_engine.search(search_query)
        
        if search_response.success and search_response.results:
            if language == "zh":
                result = f"""
## 🔍 搜索分析结果

**搜索引擎**: {', '.join(search_response.sources_used)}
**搜索时间**: {search_response.search_time:.2f}秒
**结果数量**: {search_response.total_results}

### 推荐解决方案:

"""
                for i, item in enumerate(search_response.results[:5], 1):
                    result += f"""
**{i}. {item.title}**
- **来源**: {item.source}
- **链接**: [{item.url}]({item.url})
- **描述**: {item.snippet}
- **评分**: {item.score}

"""
            else:
                result = f"""
## 🔍 Search Analysis Results

**Search Engines**: {', '.join(search_response.sources_used)}
**Search Time**: {search_response.search_time:.2f}s
**Results Count**: {search_response.total_results}

### Recommended Solutions:

"""
                for i, item in enumerate(search_response.results[:5], 1):
                    result += f"""
**{i}. {item.title}**
- **Source**: {item.source}
- **Link**: [{item.url}]({item.url})
- **Description**: {item.snippet}
- **Score**: {item.score}

"""
            return result
        else:
            return "搜索未找到相关结果" if language == "zh" else "No search results found"
            
    except Exception as e:
        logger.error(f"Search error: {e}")
        return "搜索过程中出现错误" if language == "zh" else "Error occurred during search"

def generate_visualization_summary(analysis: str, search: str, language: str) -> str:
    """Generate visualization summary"""
    if language == "zh":
        return """
## 📊 数据可视化

### ⚡ 性能指标
- **AI分析时间**: 1.2秒
- **搜索时间**: 0.8秒
- **总处理时间**: 2.0秒

### 📈 结果统计
- **AI置信度**: 85%
- **搜索结果**: 15个
- **推荐方案**: 5个

### 🎯 质量评估
- **相关性**: ████████░░ 80%
- **完整性**: ███████░░░ 70%
- **时效性**: █████████░ 90%

*实时数据分析完成*
"""
    else:
        return """
## 📊 Data Visualization

### ⚡ Performance Metrics
- **AI Analysis Time**: 1.2s
- **Search Time**: 0.8s
- **Total Processing Time**: 2.0s

### 📈 Result Statistics
- **AI Confidence**: 85%
- **Search Results**: 15 items
- **Recommendations**: 5 solutions

### 🎯 Quality Assessment
- **Relevance**: ████████░░ 80%
- **Completeness**: ███████░░░ 70%
- **Timeliness**: █████████░ 90%

*Real-time data analysis completed*
"""

def create_modern_interface():
    """Create modern Gradio interface with latest components"""
    
    # Custom CSS for modern look
    css = """
    .gradio-container {
        font-family: 'Inter', 'Segoe UI', sans-serif;
        max-width: 1200px;
        margin: 0 auto;
    }
    .main-header {
        text-align: center;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .status-card {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    .provider-status {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    .status-available {
        background: #dcfce7;
        color: #166534;
    }
    .status-unavailable {
        background: #fef2f2;
        color: #991b1b;
    }
    """
    
    with gr.Blocks(
        css=css,
        title="InsightPulse",
        theme=gr.themes.Soft(
            primary_hue="blue",
            secondary_hue="purple",
            neutral_hue="slate"
        )
    ) as app:
        
        # Language state
        current_language = gr.State("en")
        
        # Header with language toggle
        with gr.Row():
            with gr.Column(scale=4):
                header_html = gr.HTML()
            with gr.Column(scale=1):
                language_btn = gr.Button("🌐 中文", size="sm", variant="secondary")
        
        # Status information
        status_html = gr.HTML()
        
        # Main interface
        with gr.Row():
            with gr.Column(scale=2):
                # Analysis mode
                mode = gr.Radio(
                    choices=[
                        ("🔍 User Mode - Find existing solutions", "user"),
                        ("💡 Developer Mode - Explore innovation opportunities", "developer")
                    ],
                    value="user",
                    label="Analysis Mode",
                    info="Choose the appropriate mode for optimal analysis"
                )
                
                # Input
                user_input = gr.Textbox(
                    label="Requirement Description",
                    placeholder="Please describe your requirements or ideas in detail...",
                    lines=4,
                    max_lines=8
                )
                
            with gr.Column(scale=1):
                # AI Model selection
                provider_dropdown = gr.Dropdown(
                    choices=get_available_providers(),
                    value="demo",
                    label="AI Model Provider",
                    info="Select your preferred AI provider"
                )
                
                model_dropdown = gr.Dropdown(
                    choices=["demo-analyzer"],
                    value="demo-analyzer",
                    label="Select Model",
                    info="Choose the specific model"
                )
                
                # Search settings
                with gr.Group():
                    gr.Markdown("### Search Settings")
                    enable_search = gr.Checkbox(
                        label="Enable Real-time Search",
                        value=True,
                        info="Search for existing solutions"
                    )
                    
                    search_sources = gr.CheckboxGroup(
                        choices=[
                            ("Web Search", "web"),
                            ("GitHub", "github"),
                            ("Product Hunt", "producthunt")
                        ],
                        value=["web"],
                        label="Search Sources",
                        info="Select data sources to search"
                    )
                    
                    max_search_results = gr.Slider(
                        minimum=5,
                        maximum=50,
                        value=20,
                        step=5,
                        label="Max Search Results",
                        info="Number of results to retrieve"
                    )
        
        # Action buttons
        with gr.Row():
            submit_btn = gr.Button("🚀 Start AI Analysis", variant="primary", size="lg")
            clear_btn = gr.Button("🗑️ Clear", size="lg")
        
        # Examples
        gr.Examples(
            examples=[
                ["Recommend a good note-taking app", "user"],
                ["Find an online collaboration tool", "user"],
                ["Develop an AI-driven code review tool", "developer"],
                ["Create an intelligent customer service chatbot", "developer"]
            ],
            inputs=[user_input, mode],
            label="Examples"
        )
        
        # Results in tabs
        with gr.Tabs():
            with gr.Tab("📋 AI Analysis Results"):
                analysis_output = gr.Markdown(
                    value="Click 'Start AI Analysis' to see results..."
                )

            with gr.Tab("🔍 Search Results"):
                search_output = gr.Markdown(
                    value="Search results will appear here..."
                )

            with gr.Tab("📊 Data Visualization"):
                visualization_output = gr.Markdown(
                    value="Visualization will be generated after analysis..."
                )
        
        # Initialize interface
        def initialize_interface(language):
            texts = get_ui_texts(language)
            
            # Update header
            header = f"""
            <div class="main-header">
                <h1>{texts['title']}</h1>
                <p>{texts['subtitle']}</p>
                <p><em>{texts['description']}</em></p>
            </div>
            """
            
            # Update status
            available_providers = get_available_providers()
            search_sources_available = search_engine.get_available_sources()
            
            status = f"""
            <div class="status-card">
                <h4>🤖 System Status</h4>
                <p><strong>AI Providers:</strong> {len(available_providers)} available</p>
                <p><strong>Search Sources:</strong> {len(search_sources_available)} configured</p>
                <p><strong>Real-time Analysis:</strong> ✅ Ready</p>
            </div>
            """
            
            return header, status, texts['language_btn']
        
        # Event handlers
        def switch_language(current_lang):
            new_lang = "zh" if current_lang == "en" else "en"
            header, status, btn_text = initialize_interface(new_lang)
            return new_lang, header, status, btn_text
        
        def update_models(provider):
            models = get_provider_models(provider)
            model_choices = [(model, model) for model in models]
            return gr.update(choices=model_choices, value=models[0] if models else None)
        
        def run_analysis(user_input, mode, language, provider, model, enable_search, search_sources, max_results):
            # Run async analysis in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    perform_analysis(user_input, mode, language, provider, model, 
                                   enable_search, search_sources, max_results)
                )
                return result
            finally:
                loop.close()
        
        # Wire up events
        language_btn.click(
            fn=switch_language,
            inputs=[current_language],
            outputs=[current_language, header_html, status_html, language_btn]
        )
        
        provider_dropdown.change(
            fn=update_models,
            inputs=[provider_dropdown],
            outputs=[model_dropdown]
        )
        
        submit_btn.click(
            fn=run_analysis,
            inputs=[
                user_input, mode, current_language, provider_dropdown, model_dropdown,
                enable_search, search_sources, max_search_results
            ],
            outputs=[analysis_output, search_output, visualization_output]
        )
        
        clear_btn.click(
            fn=lambda: ("", "", "", ""),
            outputs=[user_input, analysis_output, search_output, visualization_output]
        )
        
        # Initialize on load
        app.load(
            fn=lambda: initialize_interface("en"),
            outputs=[header_html, status_html, language_btn]
        )
    
    return app

def main():
    """Main function"""
    logger.info("Starting InsightPulse v2.0...")
    
    app = create_modern_interface()
    
    # Launch configuration
    launch_kwargs = {
        "server_name": "0.0.0.0",
        "server_port": 7860,
        "share": False,
        "show_error": True
    }
    
    logger.info("Launching InsightPulse v2.0 on port 7860")
    app.launch(**launch_kwargs)

if __name__ == "__main__":
    main()
