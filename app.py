"""
InsightPulse - 最小化版本
只保留最基本的功能，确保稳定运行
"""

import gradio as gr
import time

def analyze_simple(user_input: str) -> str:
    """最简单的分析函数"""
    
    if not user_input.strip():
        return "请输入您的需求描述 / Please enter your requirement description"
    
    # 模拟处理时间
    time.sleep(1)
    
    result = f"""
## 📋 AI分析结果 / AI Analysis Results

**用户需求 / User Request**: {user_input}

### 需求分析 / Analysis Summary
- 🎯 核心需求识别完成 / Core requirement identified
- 📊 市场调研数据收集 / Market research completed  
- 💡 解决方案匹配分析 / Solution matching analysis
- ⭐ 可行性评估完成 / Feasibility assessment done

### 推荐解决方案 / Recommended Solutions

**1. 印象笔记 / Evernote**
- 评分 / Rating: ⭐⭐⭐⭐⭐ 4.5/5
- 特点 / Features: 全平台同步、OCR识别 / Cross-platform sync, OCR

**2. Notion**
- 评分 / Rating: ⭐⭐⭐⭐⭐ 4.7/5
- 特点 / Features: 块编辑器、团队协作 / Block editor, collaboration

**3. Obsidian**
- 评分 / Rating: ⭐⭐⭐⭐⭐ 4.6/5
- 特点 / Features: 双向链接、本地存储 / Bidirectional links, local storage

### 数据统计 / Statistics
- 处理时间 / Processing Time: 1.0秒 / 1.0s
- 相关性 / Relevance: 85%
- 推荐指数 / Recommendation Score: 4.6/5

**置信度 / Confidence**: 85%

*演示模式 - 完整功能展示 / Demo Mode - Full Feature Showcase*
"""
    
    return result

def create_minimal_interface():
    """创建最小化界面"""
    
    with gr.Blocks(title="InsightPulse") as app:
        
        # 简单标题
        gr.HTML("""
        <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; margin-bottom: 20px;">
            <h1>🔍 InsightPulse</h1>
            <p>AI-Powered Demand Exploration & Feasibility Analysis</p>
            <p><em>Demo Mode - Zero Configuration Required</em></p>
        </div>
        """)
        
        # 输入框
        user_input = gr.Textbox(
            label="需求描述 / Requirement Description",
            placeholder="请描述您的需求... / Please describe your requirements...\n\n示例 / Example: 推荐一个好用的笔记软件 / Recommend a good note-taking app",
            lines=3
        )
        
        # 按钮
        with gr.Row():
            submit_btn = gr.Button("🚀 开始分析 / Start Analysis", variant="primary", size="lg")
            clear_btn = gr.Button("🗑️ 清空 / Clear", size="lg")
        
        # 示例
        gr.Examples(
            examples=[
                "推荐一个好用的笔记软件",
                "Recommend a good note-taking app",
                "寻找项目管理工具",
                "Find a project management tool"
            ],
            inputs=[user_input],
            label="示例 / Examples"
        )
        
        # 结果显示
        output = gr.Markdown(
            value="点击'开始分析'查看结果... / Click 'Start Analysis' to see results...",
            label="📋 分析结果 / Analysis Results"
        )
        
        # 事件绑定
        submit_btn.click(
            fn=analyze_simple,
            inputs=[user_input],
            outputs=[output]
        )
        
        clear_btn.click(
            fn=lambda: ("", "点击'开始分析'查看结果... / Click 'Start Analysis' to see results..."),
            outputs=[user_input, output]
        )
        
        # 页脚
        gr.HTML("""
        <div style="text-align: center; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <p><strong>🚀 InsightPulse</strong> - AI需求分析平台 / AI Demand Analysis Platform</p>
            <p><em>零配置即用 / Zero Configuration Required</em></p>
        </div>
        """)
    
    return app

def main():
    """主函数"""
    print("🚀 Starting InsightPulse (Minimal Version)...")
    
    app = create_minimal_interface()
    
    # 最基础的启动配置
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False
    )

if __name__ == "__main__":
    main()
