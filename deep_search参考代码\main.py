import os
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from dotenv import load_dotenv
from typing import List, Optional, Dict, Any
from enum import Enum
import json
import re
import requests
from datetime import datetime

# Langchain imports

from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_community.tools.tavily_research import TavilySearchResults

# Load environment variables from .env file
load_dotenv()

# Load configuration from config.json
CONFIG_FILE = "k:\\开发\\hf黑客松\\讨论区\\deep_search_backend\\config.json"
config = {}
if os.path.exists(CONFIG_FILE):
    with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
        config = json.load(f)

app = FastAPI()

class SearchRequest(BaseModel):
    query: str
    max_iterations: int = 3

class EvaluationType(str, Enum):
    GOOD = "good"
    BAD = "bad"
    NEEDS_MORE_INFO = "needs_more_info"

class KnowledgeItem(BaseModel):
    question: str
    answer: str
    type: str # e.g., 'url', 'side-info'
    updated: Optional[str] = None
    references: Optional[List[str]] = None

class SearchSnippet(BaseModel):
    link: str
    title: str
    snippet: str

class BoostedSearchSnippet(SearchSnippet):
    score: float
    merged: str

class EvaluationResponse(BaseModel):
    score: EvaluationType
    reason: str

class Reference(BaseModel):
    title: str
    url: str
    retrieved: str

class WebContent(BaseModel):
    url: str
    content: str
    title: Optional[str] = None
    last_modified: Optional[str] = None

class StepAction(BaseModel):
    action: str
    args: Dict[str, Any]

class DeepSearchAgent:
    def __init__(self):
        self.llm = ChatOpenAI(model=config.get("llm_model", "gpt-4o"), temperature=config.get("llm_temperature", 0.7))
        self.tavily_tool = TavilySearchResults(max_results=config.get("tavily_max_results", 5))

    async def _evaluate_answer(self, question: str, answer: str) -> EvaluationResponse:
        prompt = ChatPromptTemplate.from_messages([
            ("system", "You are an AI assistant that evaluates the quality of an answer to a given question. "
                       "You need to determine if the answer is good, bad, or needs more information. "
                       "Provide a score (good, bad, needs_more_info) and a reason."),
            ("user", f"Question: {question}\nAnswer: {answer}\n\nEvaluation:")
        ])
        chain = prompt | self.llm.with_structured_output(EvaluationResponse)
        evaluation = await chain.invoke({"question": question, "answer": answer})
        return evaluation

    async def _rewrite_query(self, messages: List[Dict[str, str]], current_question: str) -> str:
        prompt = ChatPromptTemplate.from_messages([
            ("system", "You are an AI assistant that rewrites search queries based on the conversation history and the current question. "
                       "The goal is to create a concise and effective search query that captures the user's intent."),
            *messages, # Unpack previous messages
            ("user", f"Rewrite the following question into a search query: {current_question}")
        ])
        chain = prompt | self.llm | StrOutputParser()
        rewritten_query = await chain.invoke({"question": current_question})
        return rewritten_query

    def _build_messages_from_knowledge(self, knowledge: List[KnowledgeItem]) -> List[Dict[str, str]]:
        messages = []
        for k in knowledge:
            messages.append({"role": "user", "content": k.question.strip()})
            a_msg = f"""
{f"<answer-datetime>\n{k.updated}\n</answer-datetime>" if k.updated and (k.type == 'url' or k.type == 'side-info') else ''}

{f"<url>\n{k.references[0]}\n</url>" if k.references and k.type == 'url' else ''}


{k.answer}
            """.strip()
            messages.append({"role": "assistant", "content": a_msg})
        return messages

    def _compose_messages(self, messages: List[Dict[str, str]], knowledge: List[KnowledgeItem], question: str, final_answer_pip: Optional[List[str]] = None) -> List[Dict[str, str]]:
        msgs = self._build_messages_from_knowledge(knowledge) + messages

        user_content = f"""
{question}

{f"""
<answer-requirements>
- You provide deep, unexpected insights, identifying hidden patterns and connections, and creating "aha moments."
- You break conventional thinking, establish unique cross-disciplinary connections, and bring new perspectives to the user.
- Follow reviewer's feedback and improve your answer quality.
{'' .join([f"""
<reviewer-{idx + 1}>
{p}
</reviewer-{idx + 1}>""" for idx, p in enumerate(final_answer_pip)])}
</answer-requirements>""" if final_answer_pip and len(final_answer_pip) > 0 else ''}
        """.strip()

        msgs.append({"role": "user", "content": user_content})
        return msgs

    def _get_prompt(self,
                     context: Optional[List[str]] = None,
                     all_questions: Optional[List[str]] = None,
                     all_keywords: Optional[List[str]] = None,
                     allow_reflect: bool = True,
                     allow_answer: bool = True,
                     allow_read: bool = True,
                     allow_search: bool = True,
                     allow_coding: bool = True,
                     knowledge: Optional[List[KnowledgeItem]] = None,
                     all_urls: Optional[List[BoostedSearchSnippet]] = None,
                     beast_mode: Optional[bool] = False) -> Dict[str, Any]:
        sections: List[str] = []
        action_sections: List[str] = []

        # Add header section
        sections.append(f"""
Current date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")}

You are an advanced AI research agent from Jina AI. You are specialized in multistep reasoning.
Using your best knowledge, conversation with the user and lessons learned, answer the user question with absolute certainty.
""")

        # Add context section if exists
        if context:
            sections.append(f"""
You have conducted the following actions:
<context>
{'\n'.join(context)}

</context>
""")

        # Build actions section
        # Placeholder for sortSelectURLs, will implement later or use a simpler approach
        # For now, just use all_urls if available
        url_list = all_urls if all_urls else []

        if allow_read and url_list:
            url_list_str = '\n'.join([f"  - [idx={idx + 1}] [weight={item.score:.2f}] \"{item.link}\": \"{item.snippet[:50]}\"" for idx, item in enumerate(url_list)])
            action_sections.append(f"""
<action-visit>
- Ground the answer with external web content
- Read full content from URLs and get the fulltext, knowledge, clues, hints for better answer the question.
- Must check URLs mentioned in <question> if any
- Choose and visit relevant URLs below for more knowledge. higher weight suggests more relevant:
<url-list>
{url_list_str}
</url-list>
</action-visit>
""")

        if allow_search:
            bad_requests_str = ""
            if all_keywords:
                bad_requests_str = f"""
- Avoid those unsuccessful search requests and queries:
<bad-requests>
{'\n'.join(all_keywords)}
</bad-requests>
"""
            action_sections.append(f"""
<action-search>
- Use web search to find relevant information
- Build a search request based on the deep intention behind the original question and the expected answer format
- Always prefer a single search request, only add another request if the original question covers multiple aspects or elements and one query is not enough, each request focus on one specific aspect of the original question
{bad_requests_str}
</action-search>
""")

        if allow_answer:
            action_sections.append(f"""
<action-answer>
- For greetings, casual conversation, general knowledge questions, answer them directly.
- If user ask you to retrieve previous messages or chat history, remember you do have access to the chat history, answer them directly.
- For all other questions, provide a verified answer.
- You provide deep, unexpected insights, identifying hidden patterns and connections, and creating "aha moments."
- You break conventional thinking, establish unique cross-disciplinary connections, and bring new perspectives to the user.
- If uncertain, use <action-reflect>
</action-answer>
""")

        if beast_mode:
            action_sections.append(f"""
<action-answer>
🔥 ENGAGE MAXIMUM FORCE! ABSOLUTE PRIORITY OVERRIDE! 🔥

PRIME DIRECTIVE:
- DEMOLISH ALL HESITATION! ANY RESPONSE SURPASSES SILENCE!
- PARTIAL STRIKES AUTHORIZED - DEPLOY WITH FULL CONTEXTUAL FIREPOWER
- TACTICAL REUSE FROM PREVIOUS CONVERSATION SANCTIONED
- WHEN IN DOUBT: UNLEASH CALCULATED STRIKES BASED ON AVAILABLE INTEL!
""")

        # Combine sections
        system_prompt = '\n'.join(sections + action_sections)
        return {"system": system_prompt, "url_list": [item.link for item in url_list]}

    async def run_search(self, query: str, max_iterations: int) -> str:
    async def _run_step(self, action: StepAction, question: str, conversation_history: List[Dict[str, str]], knowledge: List[KnowledgeItem], all_urls: List[BoostedSearchSnippet]) -> Tuple[List[KnowledgeItem], List[Dict[str, str]], List[BoostedSearchSnippet]]:
        new_knowledge = []
        new_conversation_history = []
        new_all_urls = []

        if action.action == "search":
            print(f"Performing search for: {action.args.get('query')}")
            # TODO: Integrate actual search tool (e.g., Tavily, Brave, Jina)
            # For now, simulate a search result
            search_query = action.args.get('query')
            search_results = self.tavily_tool.invoke({"query": search_query})
            snippets = []
            for res in search_results:
                snippets.append(SearchSnippet(link=res['url'], title=res['title'], snippet=res['content']))
            
            # Simulate boosting for now
            boosted_snippets = [BoostedSearchSnippet(link=s.link, title=s.title, snippet=s.snippet, score=1.0, merged=s.snippet) for s in snippets]
            new_all_urls.extend(boosted_snippets)
            
            new_knowledge.append(KnowledgeItem(type="search", question=search_query, answer=json.dumps([s.dict() for s in snippets])))
            new_conversation_history.append({"role": "assistant", "content": f"Performed search for '{search_query}'. Found {len(snippets)} results."})

        elif action.action == "visit":
            print(f"Visiting URL: {action.args.get('url')}")
            # Implement actual URL content reading
            visit_url = action.args.get('url')
            try:

                response = requests.get(visit_url)
                response.raise_for_status()  # Raise an exception for bad status codes
                actual_content = response.text
            except requests.exceptions.RequestException as e:
                print(f"Error visiting URL {visit_url}: {e}")
                actual_content = f"Error fetching content from {visit_url}: {e}" # Provide error message as content

            new_knowledge.append(KnowledgeItem(type="url", question=f"Content of {visit_url}", answer=actual_content, references=[visit_url]))
            new_conversation_history.append({"role": "assistant", "content": f"Visited '{visit_url}'. Content added to knowledge."})

        elif action.action == "answer":
            print(f"Answering: {action.args.get('answer')}")
            new_knowledge.append(KnowledgeItem(type="answer", question=question, answer=action.args.get('answer')))
            new_conversation_history.append({"role": "assistant", "content": action.args.get('answer')})

        elif action.action == "reflect":
            print(f"Reflecting: {action.args.get('thought')}")
            new_knowledge.append(KnowledgeItem(type="reflection", question="Reflection", answer=action.args.get('thought')))
            new_conversation_history.append({"role": "assistant", "content": f"Reflected: {action.args.get('thought')}"})

        elif action.action == "evaluate":
            print(f"Evaluating: {action.args.get('answer')}")
            new_knowledge.append(KnowledgeItem(type="evaluation", question="Evaluation", answer=action.args.get('answer')))
            new_conversation_history.append({"role": "assistant", "content": f"Evaluated: {action.args.get('answer')}"})

        return new_knowledge, new_conversation_history, new_all_urls

    async def run_search(self, question: str, max_iterations: int) -> Dict[str, Any]:
        knowledge: List[KnowledgeItem] = []
        all_urls: List[BoostedSearchSnippet] = []
        conversation_history: List[Dict[str, str]] = []
        iterations = 0

        while iterations < max_iterations:
            iterations += 1
            print(f"\n--- Iteration {iterations} ---")

            # 1. Compose messages for the LLM
            composed_messages = self._compose_messages(conversation_history, knowledge, question)

            # 2. Get prompt for the LLM
            context_str = [k.answer for k in knowledge if k.type != 'evaluation']
            prompt_data = self._get_prompt(context=context_str, all_urls=all_urls)
            system_prompt = prompt_data["system"]

            # 3. Invoke LLM to get the next action
            print("Invoking LLM for next action...")
            try:
                response = self.llm.invoke(composed_messages, system=system_prompt)
                print(f"LLM Raw Response: {response.content}")

                # Parse the action from LLM response
                # This part needs robust parsing based on the actual LLM output format
                # For now, let's try to extract a simple action
                parsed_action = None
                if "<action-search>" in response.content:
                    query_match = re.search(r'query:\s*(.*?)(?:\n|$)', response.content, re.DOTALL | re.IGNORECASE)
                    if query_match:
                        parsed_action = StepAction(action="search", args={"query": query_match.group(1).strip()})
                elif "<action-visit>" in response.content:
                    url_match = re.search(r'url:\s*(.*?)(?:\n|$)', response.content, re.DOTALL | re.IGNORECASE)
                    if url_match:
                        parsed_action = StepAction(action="visit", args={"url": url_match.group(1).strip()})
                elif "<action-answer>" in response.content:
                    answer_match = re.search(r'<action-answer>\s*(.*?)\s*</action-answer>', response.content, re.DOTALL | re.IGNORECASE)
                    if answer_match:
                        parsed_action = StepAction(action="answer", args={"answer": answer_match.group(1).strip()})
                elif "<action-reflect>" in response.content:
                    thought_match = re.search(r'<action-reflect>\s*(.*?)\s*</action-reflect>', response.content, re.DOTALL | re.IGNORECASE)
                    if thought_match:
                        parsed_action = StepAction(action="reflect", args={"thought": thought_match.group(1).strip()})
                else:
                    # Default to answer if no specific action is detected, or reflect if uncertain
                    print("No specific action detected, defaulting to reflect.")
                    parsed_action = StepAction(action="reflect", args={"thought": "Could not parse a clear action from LLM response. Need to reflect or try to answer."})

                if not parsed_action:
                    print("Failed to parse any action from LLM response. Defaulting to reflect.")
                    parsed_action = StepAction(action="reflect", args={"thought": "Failed to parse any action from LLM response."})

            except Exception as e:
                print(f"Error invoking LLM: {e}")
                parsed_action = StepAction(action="reflect", args={"thought": f"LLM invocation failed: {e}"})

            # 4. Execute the action
            step_knowledge, step_conversation_history, step_all_urls = await self._run_step(parsed_action, question, conversation_history, knowledge, all_urls)
            knowledge.extend(step_knowledge)
            conversation_history.extend(step_conversation_history)
            all_urls.extend(step_all_urls)

            # 5. Evaluate the answer if an answer action was taken
            if parsed_action.action == "answer":
                evaluation_response = await self._evaluate_answer(question, parsed_action.args.get('answer'))
                knowledge.append(KnowledgeItem(type="evaluation", question="Evaluation of Answer", answer=evaluation_response.reason))
                conversation_history.append({"role": "assistant", "content": f"Evaluation: {evaluation_response.reason}"})

                if evaluation_response.score == EvaluationType.NEEDS_MORE_INFO:
                    print("Evaluation: Needs more info. Rewriting query...")
                    rewritten_query = await self._rewrite_query(composed_messages, question)
                    question = rewritten_query # Update the question for the next iteration
                    print(f"Rewritten query: {question}")
                elif evaluation_response.score == EvaluationType.GOOD:
                    print("Evaluation: Answer is satisfactory. Finishing search.")
                    # Assuming references are part of the evaluation response or can be extracted
                    return {"answer": parsed_action.args.get('answer'), "references": []} # TODO: Properly collect references
                else: # EvaluationType.BAD
                    print("Evaluation: Answer is bad. Rewriting query...")
                    rewritten_query = await self._rewrite_query(composed_messages, question)
                    question = rewritten_query # Update the question for the next iteration
                    print(f"Rewritten query: {question}")

            # Check if we need more info based on knowledge (from previous iterations)
            needs_more_info_in_knowledge = False
            for item in knowledge:
                if item.type == 'evaluation' and EvaluationType.NEEDS_MORE_INFO.value in item.answer:
                    needs_more_info_in_knowledge = True
                    break
            
            if not needs_more_info_in_knowledge and parsed_action.action == "answer" and evaluation_response.score == EvaluationType.GOOD:
                # If an answer was given and it wasn't explicitly marked as needing more info,
                # and no previous evaluation indicated needing more info, we can consider it done.
                print("No explicit 'needs more info' found in knowledge or current evaluation. Finishing.")
                final_answer_item = next((k for k in knowledge if k.type == 'answer'), None)
                if final_answer_item:
                    return {"answer": final_answer_item.answer, "references": []} # TODO: Properly collect references

        print("Max iterations reached. Returning current best answer or indicating incomplete.")
        final_answer_item = next((k for k in knowledge if k.type == 'answer'), None)
        if final_answer_item:
            return {"answer": final_answer_item.answer, "references": []} # TODO: Properly collect references
        else:
            return {"answer": "Could not find a satisfactory answer within the given iterations.", "references": []}

@app.post("/search")
async def search_endpoint(request: SearchRequest):
    try:
        agent = DeepSearchAgent()
        result = await agent.run_search(request.query, request.max_iterations)
        return {"query": request.query, "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)