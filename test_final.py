"""
Final acceptance test for InsightPulse platform.
Comprehensive testing of all implemented features.
"""

import sys
import os
import time
import json

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_project_structure():
    """Test project structure completeness"""
    print("📁 Testing project structure...")
    
    required_files = [
        "app.py",
        "app_demo.py", 
        "requirements.txt",
        "README.md",
        "DEPLOYMENT.md",
        "PROJECT_SUMMARY.md",
        ".env.example",
        "utils/__init__.py",
        "utils/config.py",
        "utils/logger.py",
        "utils/retry.py",
        "i18n/zh.json",
        "i18n/en.json",
        "i18n/loader.py",
        "llm/__init__.py",
        "llm/base.py",
        "llm/openai_client.py",
        "core/__init__.py",
        "core/demand_parser.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    else:
        print(f"✅ All {len(required_files)} required files present")
        return True

def test_configuration_system():
    """Test configuration system"""
    print("\n🔧 Testing configuration system...")
    
    try:
        from utils.config import get_config
        config = get_config()
        
        # Test basic config access
        assert config.app.app_name == "InsightPulse"
        assert config.app.app_version == "1.0.0"
        assert config.app.default_language == "zh"
        
        # Test language parsing
        languages = config.app.get_supported_languages()
        assert "zh" in languages
        assert "en" in languages
        
        # Test validation
        status = config.validate_configuration()
        assert isinstance(status, dict)
        assert "valid" in status
        
        print("✅ Configuration system working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_internationalization():
    """Test internationalization system"""
    print("\n🌍 Testing internationalization...")
    
    try:
        from i18n.loader import get_i18n, t, set_language
        
        i18n = get_i18n()
        
        # Test Chinese
        set_language("zh")
        zh_title = t("app.title")
        assert "逛逛" in zh_title or "InsightPulse" in zh_title
        
        # Test English
        set_language("en")
        en_title = t("app.title")
        assert "InsightPulse" in en_title
        
        # Test missing key handling
        missing_key = t("non.existent.key")
        assert missing_key == "non.existent.key"
        
        # Test available languages
        languages = i18n.get_available_languages()
        assert len(languages) >= 2
        
        print("✅ Internationalization system working correctly")
        return True
        
    except Exception as e:
        print(f"❌ I18n test failed: {e}")
        return False

def test_llm_interface():
    """Test LLM interface layer"""
    print("\n🤖 Testing LLM interface...")
    
    try:
        from llm.base import LLMManager, LLMMessage, LLMConfig, LLMProvider
        from llm.openai_client import OpenAIClient, OpenAIPrompts
        
        # Test message creation
        message = LLMMessage(role="user", content="Test message")
        assert message.role == "user"
        assert message.content == "Test message"
        
        # Test config creation
        config = LLMConfig(model="test-model", temperature=0.7)
        assert config.model == "test-model"
        assert config.temperature == 0.7
        
        # Test manager
        manager = LLMManager()
        assert len(manager.get_available_providers()) == 0
        
        # Test prompts
        zh_prompt = OpenAIPrompts.demand_analysis_prompt("zh")
        en_prompt = OpenAIPrompts.demand_analysis_prompt("en")
        assert "需求分析" in zh_prompt
        assert "demand analysis" in en_prompt.lower()
        
        print("✅ LLM interface layer working correctly")
        return True
        
    except Exception as e:
        print(f"❌ LLM interface test failed: {e}")
        return False

def test_demand_parser():
    """Test demand parser functionality"""
    print("\n🔍 Testing demand parser...")
    
    try:
        from core.demand_parser import DemandParser, UserMode, DemandCategory
        from llm.base import LLMManager
        
        # Create parser with mock LLM manager
        llm_manager = LLMManager()
        parser = DemandParser(llm_manager)
        
        # Test Chinese input
        zh_input = "推荐一个好用的笔记软件"
        zh_analysis = parser._quick_classify(zh_input, "zh")
        
        assert zh_analysis["user_mode"] == UserMode.USER
        assert zh_analysis["category"] == DemandCategory.PRODUCTIVITY
        assert len(zh_analysis["keywords"]) > 0
        
        # Test English input
        en_input = "develop an AI code review tool"
        en_analysis = parser._quick_classify(en_input, "en")
        
        assert en_analysis["user_mode"] == UserMode.DEVELOPER
        assert en_analysis["category"] == DemandCategory.DEVELOPMENT
        
        # Test fallback analysis
        fallback = parser._create_fallback_analysis("test input", "zh")
        assert fallback.raw_input == "test input"
        assert fallback.language == "zh"
        
        print("✅ Demand parser working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Demand parser test failed: {e}")
        return False

def test_demo_application():
    """Test demo application functionality"""
    print("\n🎨 Testing demo application...")
    
    try:
        from app_demo import simulate_demand_analysis, format_demo_analysis, generate_demo_recommendations
        
        # Test simulation
        analysis = simulate_demand_analysis("推荐笔记软件", "zh")
        assert "user_mode" in analysis
        assert "category" in analysis
        assert "keywords" in analysis
        
        # Test formatting
        formatted = format_demo_analysis(analysis, "zh")
        assert "需求分析结果" in formatted
        assert "用户意图" in formatted
        
        # Test recommendations
        recommendations = generate_demo_recommendations(analysis, "zh")
        assert len(recommendations) > 0
        
        print("✅ Demo application working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Demo application test failed: {e}")
        return False

def test_error_handling():
    """Test error handling and logging"""
    print("\n🛡️ Testing error handling...")
    
    try:
        from utils.logger import get_logger, get_api_logger
        from utils.retry import exponential_backoff_with_jitter
        
        # Test logger creation
        logger = get_logger("TestLogger")
        api_logger = get_api_logger("TestAPILogger")
        
        # Test logging methods
        logger.info("Test info message")
        logger.warning("Test warning message")
        api_logger.log_api_request("test_provider", "test_endpoint")
        
        # Test retry decorator
        @exponential_backoff_with_jitter(max_attempts=2)
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"
        
        print("✅ Error handling and logging working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def test_gradio_integration():
    """Test Gradio integration"""
    print("\n🎭 Testing Gradio integration...")
    
    try:
        import gradio as gr
        from app_demo import create_demo_interface
        
        # Test interface creation
        interface = create_demo_interface()
        assert interface is not None
        
        print(f"✅ Gradio integration working (version: {gr.__version__})")
        return True
        
    except Exception as e:
        print(f"❌ Gradio integration test failed: {e}")
        return False

def test_deployment_readiness():
    """Test deployment readiness"""
    print("\n🚀 Testing deployment readiness...")
    
    try:
        # Check required files for deployment
        deployment_files = [
            "app_demo.py",
            "requirements.txt", 
            "README.md"
        ]
        
        for file_path in deployment_files:
            assert os.path.exists(file_path), f"Missing deployment file: {file_path}"
        
        # Check requirements.txt content
        with open("requirements.txt", "r") as f:
            requirements = f.read()
            assert "gradio" in requirements
            assert "pydantic" in requirements
            assert "python-dotenv" in requirements
        
        # Check README.md content
        with open("README.md", "r", encoding="utf-8") as f:
            readme = f.read()
            assert "InsightPulse" in readme
            assert "逛逛" in readme
        
        print("✅ Deployment readiness verified")
        return True
        
    except Exception as e:
        print(f"❌ Deployment readiness test failed: {e}")
        return False

def run_performance_test():
    """Run basic performance test"""
    print("\n⚡ Running performance test...")
    
    try:
        from app_demo import simulate_demand_analysis
        
        # Test response time
        start_time = time.time()
        
        for i in range(10):
            analysis = simulate_demand_analysis(f"test input {i}", "zh")
            assert analysis is not None
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 10
        
        print(f"✅ Average analysis time: {avg_time:.3f} seconds")
        
        if avg_time < 0.1:
            print("🚀 Excellent performance!")
        elif avg_time < 0.5:
            print("✅ Good performance")
        else:
            print("⚠️ Performance could be improved")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def main():
    """Run all final tests"""
    print("🎯 InsightPulse Final Acceptance Test")
    print("=" * 50)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Configuration System", test_configuration_system),
        ("Internationalization", test_internationalization),
        ("LLM Interface", test_llm_interface),
        ("Demand Parser", test_demand_parser),
        ("Demo Application", test_demo_application),
        ("Error Handling", test_error_handling),
        ("Gradio Integration", test_gradio_integration),
        ("Deployment Readiness", test_deployment_readiness),
        ("Performance", run_performance_test)
    ]
    
    passed = 0
    total = len(tests)
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                results.append((test_name, "✅ PASS"))
            else:
                results.append((test_name, "❌ FAIL"))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, f"❌ ERROR: {e}"))
    
    # Print summary
    print("\n" + "="*60)
    print("📊 FINAL TEST RESULTS")
    print("="*60)
    
    for test_name, result in results:
        print(f"{result:<20} {test_name}")
    
    print(f"\n📈 Overall Score: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! 🎉")
        print("\n✅ Project is ready for deployment!")
        print("\n📋 Next Steps:")
        print("1. 🔑 Add OpenAI API key to .env for full functionality")
        print("2. 🚀 Deploy to Hugging Face Spaces")
        print("3. 🌐 Share with the community")
        print("4. 📈 Monitor usage and gather feedback")
        
    elif passed >= total * 0.8:
        print("\n✅ Most tests passed! Project is nearly ready.")
        print("🔧 Please fix the failing tests before deployment.")
        
    else:
        print("\n❌ Multiple tests failed. Please review and fix issues.")
        print("🛠️ Check the error messages above for details.")
    
    print(f"\n🏁 Test completed at {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
