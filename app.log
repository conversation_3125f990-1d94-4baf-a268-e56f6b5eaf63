2025-06-03 15:40:30,534 - <PERSON><PERSON>ogger - INFO - Test info message
2025-06-03 15:40:30,534 - <PERSON><PERSON>ogger - WARNING - Test warning message
2025-06-03 15:40:30,534 - TestAPILogger - INFO - API Request to test_provider | Context: {"provider": "test_provider", "endpoint": "test_endpoint", "method": "POST", "timestamp": "2025-06-03T15:40:30.534590"}
2025-06-03 15:40:30,538 - I18nLoader - INFO - Loaded zh language resources
2025-06-03 15:40:30,538 - I18nLoader - INFO - Loaded en language resources
2025-06-03 15:40:30,538 - I18nLoader - INFO - Language set to: zh
2025-06-03 15:40:30,539 - I18nLoader - INFO - Language set to: en
2025-06-03 15:40:33,913 - httpx - INFO - HTTP Request: GET https://api.gradio.app/gradio-messaging/en "HTTP/1.1 200 OK"
2025-06-03 15:42:33,391 - <PERSON><PERSON><PERSON><PERSON>oa<PERSON> - INFO - Loaded zh language resources
2025-06-03 15:42:33,391 - I18nLoader - INFO - Loaded en language resources
2025-06-03 15:42:33,555 - DemoApp - INFO - Starting InsightPulse Demo Application...
2025-06-03 15:42:33,627 - DemoApp - INFO - Launching demo application on port 7860
2025-06-03 15:42:33,734 - httpx - INFO - HTTP Request: GET http://localhost:7860/startup-events "HTTP/1.1 200 OK"
2025-06-03 15:42:33,750 - httpx - INFO - HTTP Request: HEAD http://localhost:7860/ "HTTP/1.1 200 OK"
2025-06-03 15:42:34,480 - httpx - INFO - HTTP Request: GET https://api.gradio.app/gradio-messaging/en "HTTP/1.1 200 OK"
2025-06-03 15:42:35,069 - httpx - INFO - HTTP Request: GET https://checkip.amazonaws.com/ "HTTP/1.1 200 "
2025-06-03 15:42:35,157 - httpx - INFO - HTTP Request: GET https://checkip.amazonaws.com/ "HTTP/1.1 200 "
2025-06-03 15:42:35,950 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-06-03 15:46:01,701 - I18nLoader - INFO - Loaded zh language resources
2025-06-03 15:46:01,701 - I18nLoader - INFO - Loaded en language resources
2025-06-03 15:46:01,702 - I18nLoader - INFO - Language set to: zh
2025-06-03 15:46:01,702 - I18nLoader - INFO - Language set to: en
2025-06-03 15:46:01,702 - I18nLoader - WARNING - Translation key not found: non.existent.key for language: en
2025-06-03 15:46:03,969 - TestLogger - INFO - Test info message
2025-06-03 15:46:03,970 - TestLogger - WARNING - Test warning message
2025-06-03 15:46:03,970 - TestAPILogger - INFO - API Request to test_provider | Context: {"provider": "test_provider", "endpoint": "test_endpoint", "method": "POST", "timestamp": "2025-06-03T15:46:03.970610"}
2025-06-03 15:46:05,204 - httpx - INFO - HTTP Request: GET https://api.gradio.app/gradio-messaging/en "HTTP/1.1 200 OK"
2025-06-03 15:46:05,463 - httpx - INFO - HTTP Request: GET https://checkip.amazonaws.com/ "HTTP/1.1 200 "
2025-06-03 15:46:06,579 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
