"""
Demo version of InsightPulse platform without LLM dependency.
Shows the basic functionality and UI design.
"""

import gradio as gr
import os
import json
from typing import Optional, Tuple, Dict, Any
import time
import random

# Import our modules
from utils.config import get_config
from utils.logger import get_logger
from i18n.loader import get_i18n, t, set_language
from core.demand_parser import UserMode, DemandCategory

# Initialize components
config = get_config()
logger = get_logger("DemoApp")
i18n = get_i18n()

# Demo data for different categories
DEMO_SOLUTIONS = {
    DemandCategory.PRODUCTIVITY: {
        "zh": [
            {
                "name": "印象笔记 (Evernote)",
                "rating": "4.5/5",
                "features": "全平台同步、网页剪藏、OCR识别",
                "pros": "功能全面、生态成熟",
                "cons": "免费版限制较多",
                "website": "https://evernote.com"
            },
            {
                "name": "Notion",
                "rating": "4.7/5", 
                "features": "块编辑器、数据库、协作",
                "pros": "高度灵活、协作友好",
                "cons": "学习曲线较陡",
                "website": "https://notion.so"
            },
            {
                "name": "Obsidian",
                "rating": "4.6/5",
                "features": "双向链接、本地存储、插件生态",
                "pros": "本地优先、双向链接强大",
                "cons": "界面相对复杂",
                "website": "https://obsidian.md"
            }
        ],
        "en": [
            {
                "name": "Evernote",
                "rating": "4.5/5",
                "features": "Cross-platform sync, web clipper, OCR",
                "pros": "Comprehensive features, mature ecosystem",
                "cons": "Limited free version",
                "website": "https://evernote.com"
            },
            {
                "name": "Notion",
                "rating": "4.7/5",
                "features": "Block editor, databases, collaboration",
                "pros": "Highly flexible, collaboration-friendly",
                "cons": "Steep learning curve",
                "website": "https://notion.so"
            },
            {
                "name": "Obsidian", 
                "rating": "4.6/5",
                "features": "Bidirectional links, local storage, plugins",
                "pros": "Local-first, powerful linking",
                "cons": "Complex interface",
                "website": "https://obsidian.md"
            }
        ]
    },
    DemandCategory.DEVELOPMENT: {
        "zh": [
            {
                "name": "GitHub Copilot",
                "rating": "4.4/5",
                "features": "AI代码补全、多语言支持",
                "pros": "智能补全、学习能力强",
                "cons": "需要付费订阅",
                "website": "https://github.com/features/copilot"
            },
            {
                "name": "CodeT5",
                "rating": "4.2/5",
                "features": "代码理解、生成、翻译",
                "pros": "开源免费、多任务支持",
                "cons": "需要本地部署",
                "website": "https://github.com/salesforce/CodeT5"
            }
        ],
        "en": [
            {
                "name": "GitHub Copilot",
                "rating": "4.4/5",
                "features": "AI code completion, multi-language support",
                "pros": "Smart completion, strong learning ability",
                "cons": "Requires paid subscription",
                "website": "https://github.com/features/copilot"
            },
            {
                "name": "CodeT5",
                "rating": "4.2/5",
                "features": "Code understanding, generation, translation",
                "pros": "Open source, multi-task support",
                "cons": "Requires local deployment",
                "website": "https://github.com/salesforce/CodeT5"
            }
        ]
    }
}

def simulate_demand_analysis(user_input: str, language: str) -> Dict[str, Any]:
    """Simulate demand analysis without LLM"""
    
    # Simple keyword-based analysis
    input_lower = user_input.lower()
    
    # Determine user mode
    developer_keywords = ["开发", "develop", "创建", "create", "构建", "build", "AI", "应用", "app"]
    user_keywords = ["推荐", "recommend", "寻找", "find", "需要", "need", "好用", "useful"]
    
    developer_score = sum(1 for kw in developer_keywords if kw in input_lower)
    user_score = sum(1 for kw in user_keywords if kw in input_lower)
    
    user_mode = UserMode.DEVELOPER if developer_score > user_score else UserMode.USER
    
    # Determine category
    if any(kw in input_lower for kw in ["笔记", "note", "办公", "office", "效率", "productivity"]):
        category = DemandCategory.PRODUCTIVITY
    elif any(kw in input_lower for kw in ["开发", "develop", "代码", "code", "编程", "programming"]):
        category = DemandCategory.DEVELOPMENT
    else:
        category = DemandCategory.PRODUCTIVITY  # Default
    
    # Extract keywords
    keywords = [word for word in input_lower.split() if len(word) > 1][:5]
    
    return {
        "user_intent": user_input,
        "category": category,
        "keywords": keywords,
        "user_mode": user_mode,
        "confidence": 0.8
    }

def analyze_demand_demo(user_input: str, mode: str, language: str) -> Tuple[str, str, str]:
    """Demo version of demand analysis"""
    
    if not user_input.strip():
        error_msg = "请输入您的需求描述" if language == "zh" else "Please enter your requirement description"
        return error_msg, "", ""
    
    try:
        # Set language
        set_language(language)
        
        # Simulate processing time
        time.sleep(1)
        
        # Simulate analysis
        analysis = simulate_demand_analysis(user_input, language)
        
        # Format results
        analysis_result = format_demo_analysis(analysis, language)
        recommendations = generate_demo_recommendations(analysis, language)
        visualization = generate_demo_visualization(analysis, language)
        
        return analysis_result, recommendations, visualization
        
    except Exception as e:
        logger.error(f"Demo analysis error: {e}")
        error_msg = "分析过程中出现错误" if language == "zh" else "Error occurred during analysis"
        return error_msg, "", ""

def format_demo_analysis(analysis: Dict[str, Any], language: str) -> str:
    """Format demo analysis results"""
    
    if language == "zh":
        mode_text = "开发者模式" if analysis["user_mode"] == UserMode.DEVELOPER else "普通用户模式"
        category_text = {
            DemandCategory.PRODUCTIVITY: "效率工具",
            DemandCategory.DEVELOPMENT: "开发工具",
            DemandCategory.EDUCATION: "教育",
            DemandCategory.ENTERTAINMENT: "娱乐",
            DemandCategory.OTHER: "其他"
        }.get(analysis["category"], "其他")
        
        return f"""
## 📋 需求分析结果

**用户意图**: {analysis["user_intent"]}

**需求领域**: {category_text}

**关键词**: {', '.join(analysis["keywords"])}

**用户模式**: {mode_text}

**分析置信度**: {analysis["confidence"]:.1%}

---
*这是演示版本的分析结果。完整版本将使用大语言模型进行更深入的分析。*
"""
    else:
        mode_text = "Developer Mode" if analysis["user_mode"] == UserMode.DEVELOPER else "User Mode"
        category_text = {
            DemandCategory.PRODUCTIVITY: "Productivity Tools",
            DemandCategory.DEVELOPMENT: "Development Tools", 
            DemandCategory.EDUCATION: "Education",
            DemandCategory.ENTERTAINMENT: "Entertainment",
            DemandCategory.OTHER: "Other"
        }.get(analysis["category"], "Other")
        
        return f"""
## 📋 Analysis Results

**User Intent**: {analysis["user_intent"]}

**Domain Category**: {category_text}

**Keywords**: {', '.join(analysis["keywords"])}

**User Mode**: {mode_text}

**Confidence Score**: {analysis["confidence"]:.1%}

---
*This is a demo version of the analysis. The full version will use large language models for deeper analysis.*
"""

def generate_demo_recommendations(analysis: Dict[str, Any], language: str) -> str:
    """Generate demo recommendations"""
    
    category = analysis["category"]
    user_mode = analysis["user_mode"]
    
    if user_mode == UserMode.USER:
        # Show existing solutions
        solutions = DEMO_SOLUTIONS.get(category, {}).get(language, [])
        
        if language == "zh":
            result = "## 🔍 现有解决方案推荐\n\n"
            if solutions:
                for i, solution in enumerate(solutions, 1):
                    result += f"""
### {i}. {solution['name']}
- **评分**: {solution['rating']}
- **核心功能**: {solution['features']}
- **优势**: {solution['pros']}
- **劣势**: {solution['cons']}
- **官网**: [{solution['name']}]({solution['website']})

"""
            else:
                result += "暂无相关解决方案数据。\n"
            
            result += "\n*注：这是演示数据。完整版本将实时搜索最新的解决方案。*"
        else:
            result = "## 🔍 Existing Solution Recommendations\n\n"
            if solutions:
                for i, solution in enumerate(solutions, 1):
                    result += f"""
### {i}. {solution['name']}
- **Rating**: {solution['rating']}
- **Core Features**: {solution['features']}
- **Pros**: {solution['pros']}
- **Cons**: {solution['cons']}
- **Website**: [{solution['name']}]({solution['website']})

"""
            else:
                result += "No relevant solution data available.\n"
            
            result += "\n*Note: This is demo data. The full version will search for the latest solutions in real-time.*"
    
    else:
        # Developer mode - show innovation opportunities
        if language == "zh":
            result = f"""
## 💡 创新机会分析

### 🎯 推荐行动
💎 **建议探索新方向**

### 📊 可行性评估
- **技术可行性**: 85% - 技术相对成熟
- **市场需求**: 78% - 存在明确需求
- **竞争程度**: 65% - 竞争适中，有差异化空间
- **法律风险**: 90% - 风险较低
- **成本控制**: 70% - 开发成本可控

### 🚀 下一步建议
1. 进行更详细的市场调研
2. 制作最小可行产品(MVP)
3. 寻找目标用户进行验证
4. 考虑技术实现方案

### ⚠️ 风险提醒
- 注意用户隐私保护
- 关注竞品动态
- 确保技术方案的可扩展性

*注：这是基于关键词的简化分析。完整版本将提供更深入的市场和技术分析。*
"""
        else:
            result = f"""
## 💡 Innovation Opportunity Analysis

### 🎯 Recommended Action
💎 **Suggest Exploring New Direction**

### 📊 Feasibility Assessment
- **Technical Feasibility**: 85% - Technology is relatively mature
- **Market Demand**: 78% - Clear demand exists
- **Competition Level**: 65% - Moderate competition with differentiation opportunities
- **Legal Risk**: 90% - Low risk
- **Cost Control**: 70% - Development costs are manageable

### 🚀 Next Steps
1. Conduct more detailed market research
2. Create a Minimum Viable Product (MVP)
3. Find target users for validation
4. Consider technical implementation solutions

### ⚠️ Risk Warning
- Pay attention to user privacy protection
- Monitor competitor dynamics
- Ensure scalability of technical solutions

*Note: This is a simplified analysis based on keywords. The full version will provide deeper market and technical analysis.*
"""
    
    return result

def generate_demo_visualization(analysis: Dict[str, Any], language: str) -> str:
    """Generate demo visualization description"""
    
    if language == "zh":
        return """
## 📊 可视化分析

### 🎯 可行性雷达图
```
技术可行性: ████████░░ 85%
市场需求:   ███████░░░ 78%
竞争程度:   ██████░░░░ 65%
法律风险:   █████████░ 90%
成本控制:   ███████░░░ 70%
市场接受度: ████████░░ 82%
```

### 📈 趋势分析
- 该领域在过去12个月内搜索量增长 **23%**
- 相关产品发布数量增加 **15%**
- 用户满意度平均分: **4.2/5**

*注：可视化功能正在开发中。完整版本将提供交互式图表和更详细的数据分析。*
"""
    else:
        return """
## 📊 Visualization Analysis

### 🎯 Feasibility Radar Chart
```
Technical Feasibility: ████████░░ 85%
Market Demand:         ███████░░░ 78%
Competition Level:     ██████░░░░ 65%
Legal Risk:           █████████░ 90%
Cost Control:         ███████░░░ 70%
Market Acceptance:    ████████░░ 82%
```

### 📈 Trend Analysis
- Search volume in this field has grown **23%** in the past 12 months
- Number of related product releases increased by **15%**
- Average user satisfaction score: **4.2/5**

*Note: Visualization features are under development. The full version will provide interactive charts and more detailed data analysis.*
"""

def create_demo_interface():
    """Create demo Gradio interface"""
    
    css = """
    .gradio-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    .main-header {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    .demo-notice {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        color: #856404;
    }
    """
    
    with gr.Blocks(css=css, title="逛逛 - InsightPulse (Demo)", theme=gr.themes.Soft()) as app:
        
        # Header
        gr.HTML("""
        <div class="main-header">
            <h1>🔍 逛逛 (InsightPulse) - 演示版</h1>
            <p>需求探索与可行性分析平台</p>
            <p><em>Demand Exploration & Feasibility Analysis Platform - Demo Version</em></p>
        </div>
        """)
        
        # Demo notice
        gr.HTML("""
        <div class="demo-notice">
            <h4>📢 演示说明 / Demo Notice</h4>
            <p><strong>中文</strong>: 这是演示版本，使用模拟数据展示平台功能。完整版本将集成大语言模型进行深度分析。</p>
            <p><strong>English</strong>: This is a demo version using simulated data to showcase platform features. The full version will integrate large language models for deep analysis.</p>
        </div>
        """)
        
        # Language and mode selectors
        with gr.Row():
            language = gr.Dropdown(
                choices=[("中文", "zh"), ("English", "en")],
                value="zh",
                label="语言 / Language",
                interactive=True
            )
            
            mode = gr.Radio(
                choices=[
                    ("🔍 普通用户模式", "user"),
                    ("💡 开发者模式", "developer")
                ],
                value="user",
                label="使用模式 / Mode"
            )
        
        # Input section
        with gr.Row():
            user_input = gr.Textbox(
                label="需求描述 / Requirement Description",
                placeholder="请详细描述您的需求...\nPlease describe your requirements...",
                lines=3,
                max_lines=6
            )
        
        with gr.Row():
            submit_btn = gr.Button("🚀 开始分析 / Start Analysis", variant="primary")
            clear_btn = gr.Button("🗑️ 清空 / Clear")
        
        # Examples
        gr.Examples(
            examples=[
                ["推荐一个好用的笔记软件", "user", "zh"],
                ["开发一个AI代码审查工具", "developer", "zh"],
                ["Recommend a good note-taking app", "user", "en"],
                ["Develop an AI code review tool", "developer", "en"]
            ],
            inputs=[user_input, mode, language],
            label="示例 / Examples"
        )
        
        # Results
        with gr.Row():
            with gr.Column():
                analysis_output = gr.Markdown(label="📋 分析结果 / Analysis Results")
            with gr.Column():
                recommendations_output = gr.Markdown(label="💡 推荐建议 / Recommendations")
        
        with gr.Row():
            visualization_output = gr.Markdown(label="📊 可视化分析 / Visualization")
        
        # Event handlers
        submit_btn.click(
            fn=analyze_demand_demo,
            inputs=[user_input, mode, language],
            outputs=[analysis_output, recommendations_output, visualization_output]
        )
        
        clear_btn.click(
            fn=lambda: ("", "", "", ""),
            outputs=[user_input, analysis_output, recommendations_output, visualization_output]
        )
        
        # Footer
        gr.HTML(f"""
        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <p><strong>🚀 技术支持 / Powered by</strong></p>
            <p>Gradio • Python • Hugging Face Spaces</p>
            <p><em>HF黑客松参赛作品 / HF Hackathon Project</em></p>
            <p>版本 / Version: {config.app.app_version} (Demo)</p>
        </div>
        """)
    
    return app

def main():
    """Main function for demo app"""
    logger.info("Starting InsightPulse Demo Application...")
    
    app = create_demo_interface()
    
    # Launch configuration
    launch_kwargs = {
        "server_name": "0.0.0.0",
        "server_port": 7860,
        "share": False,
        "show_error": True
    }
    
    logger.info("Launching demo application on port 7860")
    app.launch(**launch_kwargs)

if __name__ == "__main__":
    main()
