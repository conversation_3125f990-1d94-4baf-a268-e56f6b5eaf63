# 🔑 环境变量配置指南

## 📋 需要填入的环境变量

### 🤖 AI模型提供商 (按需选择)

#### OpenAI (推荐)
```env
OPENAI_API_KEY=sk-your_openai_api_key_here
```
- 获取地址: https://platform.openai.com/api-keys
- 支持模型: GPT-4o, GPT-4o-mini, GPT-4-turbo, GPT-3.5-turbo

#### Anthropic <PERSON>
```env
ANTHROPIC_API_KEY=sk-ant-your_anthropic_key_here
```
- 获取地址: https://console.anthropic.com/
- 支持模型: <PERSON>-3.5-<PERSON><PERSON>, <PERSON>-3-<PERSON><PERSON>, <PERSON>-3-<PERSON>

#### Google Gemini
```env
GOOGLE_API_KEY=your_google_api_key_here
```
- 获取地址: https://makersuite.google.com/app/apikey
- 支持模型: Gemini-1.5-Pro, Gemini-1.5-Flash

#### DeepSeek (中文AI)
```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```
- 获取地址: https://platform.deepseek.com/
- 支持模型: DeepSeek-Chat, DeepSeek-Coder

#### OpenRouter (多模型聚合)
```env
OPENROUTER_API_KEY=sk-or-your_openrouter_key_here
```
- 获取地址: https://openrouter.ai/keys
- 支持模型: 访问多个AI提供商的模型

### 🔍 搜索引擎 (按需选择)

#### Serper Google Search (推荐)
```env
SERPER_API_KEY=your_serper_api_key_here
```
- 获取地址: https://serper.dev/
- 功能: Google搜索结果API
- 免费额度: 2500次查询/月

#### GitHub (开源项目搜索)
```env
GITHUB_TOKEN=ghp_your_github_token_here
```
- 获取地址: https://github.com/settings/tokens
- 功能: 搜索GitHub仓库和项目
- 权限: public_repo (只读)

#### Product Hunt (产品发现)
```env
PRODUCTHUNT_API_KEY=your_producthunt_key_here
```
- 获取地址: https://api.producthunt.com/v2/oauth/applications
- 功能: 搜索产品和创业项目

## 🚀 快速开始

### 最小配置 (仅演示)
不需要任何API密钥，应用会以演示模式运行：
```bash
# 直接运行，使用模拟数据
python app.py
```

### 基础配置 (推荐)
只需要一个AI提供商：
```env
# 选择其中一个
OPENAI_API_KEY=your_key_here
# 或
ANTHROPIC_API_KEY=your_key_here
# 或
GOOGLE_API_KEY=your_key_here
```

### 完整配置 (最佳体验)
AI + 搜索功能：
```env
# AI模型 (选择一个主要的)
OPENAI_API_KEY=your_openai_key_here

# 搜索引擎 (推荐)
SERPER_API_KEY=your_serper_key_here
GITHUB_TOKEN=your_github_token_here
```

## 📝 配置步骤

### 1. 创建 .env 文件
```bash
cp .env.example .env
```

### 2. 编辑 .env 文件
```bash
# 使用文本编辑器打开 .env 文件
# 填入您获得的API密钥
```

### 3. 启动应用
```bash
python app.py
```

### 4. 访问应用
打开浏览器访问: http://localhost:7860

## 🎯 推荐配置

### 个人用户
```env
OPENAI_API_KEY=your_openai_key_here
SERPER_API_KEY=your_serper_key_here
```

### 开发者
```env
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
SERPER_API_KEY=your_serper_key_here
GITHUB_TOKEN=your_github_token_here
```

### 企业用户
```env
# 多个AI提供商
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here

# 完整搜索功能
SERPER_API_KEY=your_serper_key_here
GITHUB_TOKEN=your_github_token_here
PRODUCTHUNT_API_KEY=your_producthunt_key_here
```

## ❓ 常见问题

### Q: 不填任何API密钥可以使用吗？
A: 可以！应用会以演示模式运行，使用模拟数据展示功能。

### Q: 只填一个OpenAI密钥够用吗？
A: 够用！这样可以使用真实的AI分析功能，搜索功能会使用演示数据。

### Q: 如何获得免费的API密钥？
A: 
- OpenAI: 新用户有免费额度
- Serper: 每月2500次免费查询
- GitHub: 免费用户可以创建Personal Access Token

### Q: API密钥安全吗？
A: 
- 本地运行：密钥只在您的电脑上
- Hugging Face部署：使用Secrets功能安全存储

## 🔒 安全提醒

1. **不要在代码中硬编码API密钥**
2. **使用 .env 文件存储密钥**
3. **不要将 .env 文件提交到Git**
4. **定期轮换API密钥**
5. **监控API使用量和费用**

---

**总结**: 最少只需要一个OpenAI API密钥就能获得完整的AI分析体验！
