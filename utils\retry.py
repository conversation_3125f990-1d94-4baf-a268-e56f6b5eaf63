"""
Retry mechanism utilities for InsightPulse platform.
Implements exponential backoff with jitter for robust API calls.
"""

import asyncio
import random
import time
from typing import Callable, Any, Optional, Type, Union, List
from functools import wraps
import logging
from tenacity import (
    retry, 
    stop_after_attempt, 
    wait_exponential, 
    retry_if_exception_type,
    before_sleep_log,
    after_log
)

from .logger import get_api_logger

logger = get_api_logger("RetryMechanism")

class RetryConfig:
    """Configuration for retry mechanisms"""
    
    # Default retry settings
    DEFAULT_MAX_ATTEMPTS = 3
    DEFAULT_MIN_WAIT = 1
    DEFAULT_MAX_WAIT = 60
    DEFAULT_MULTIPLIER = 2
    DEFAULT_JITTER = True
    
    # API-specific settings
    LLM_MAX_ATTEMPTS = 5
    LLM_MIN_WAIT = 2
    LLM_MAX_WAIT = 120
    
    EXTERNAL_API_MAX_ATTEMPTS = 3
    EXTERNAL_API_MIN_WAIT = 1
    EXTERNAL_API_MAX_WAIT = 30

# Common exceptions that should trigger retries
RETRY<PERSON>LE_EXCEPTIONS = (
    Connection<PERSON>rro<PERSON>,
    TimeoutError,
    OS<PERSON>rror,
    # Add specific API exceptions here
)

# HTTP status codes that should trigger retries
RETRYABLE_HTTP_CODES = [429, 500, 502, 503, 504]

def exponential_backoff_with_jitter(
    max_attempts: int = RetryConfig.DEFAULT_MAX_ATTEMPTS,
    min_wait: float = RetryConfig.DEFAULT_MIN_WAIT,
    max_wait: float = RetryConfig.DEFAULT_MAX_WAIT,
    multiplier: float = RetryConfig.DEFAULT_MULTIPLIER,
    jitter: bool = RetryConfig.DEFAULT_JITTER,
    retryable_exceptions: tuple = RETRYABLE_EXCEPTIONS
):
    """
    Decorator for exponential backoff with jitter retry mechanism.
    
    Args:
        max_attempts: Maximum number of retry attempts
        min_wait: Minimum wait time between retries (seconds)
        max_wait: Maximum wait time between retries (seconds)
        multiplier: Exponential backoff multiplier
        jitter: Whether to add random jitter to wait times
        retryable_exceptions: Tuple of exceptions that should trigger retries
    """
    def decorator(func: Callable) -> Callable:
        @retry(
            stop=stop_after_attempt(max_attempts),
            wait=wait_exponential(
                multiplier=multiplier,
                min=min_wait,
                max=max_wait
            ),
            retry=retry_if_exception_type(retryable_exceptions),
            before_sleep=before_sleep_log(logger.logger, logging.WARNING),
            after=after_log(logger.logger, logging.INFO)
        )
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                logger.log_api_error(
                    provider=getattr(func, '__module__', 'unknown'),
                    endpoint=func.__name__,
                    error=e,
                    retry_count=getattr(wrapper.retry, 'statistics', {}).get('attempt_number', 0)
                )
                raise
        
        return wrapper
    return decorator

def llm_retry(func: Callable) -> Callable:
    """Specialized retry decorator for LLM API calls"""
    return exponential_backoff_with_jitter(
        max_attempts=RetryConfig.LLM_MAX_ATTEMPTS,
        min_wait=RetryConfig.LLM_MIN_WAIT,
        max_wait=RetryConfig.LLM_MAX_WAIT,
        retryable_exceptions=RETRYABLE_EXCEPTIONS + (
            # Add LLM-specific exceptions here
            Exception,  # Temporary - replace with specific exceptions
        )
    )(func)

def external_api_retry(func: Callable) -> Callable:
    """Specialized retry decorator for external API calls"""
    return exponential_backoff_with_jitter(
        max_attempts=RetryConfig.EXTERNAL_API_MAX_ATTEMPTS,
        min_wait=RetryConfig.EXTERNAL_API_MIN_WAIT,
        max_wait=RetryConfig.EXTERNAL_API_MAX_WAIT
    )(func)

class CircuitBreaker:
    """
    Circuit breaker pattern implementation for API calls.
    Prevents cascading failures by temporarily stopping calls to failing services.
    """
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: Type[Exception] = Exception
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    def __call__(self, func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            if self.state == 'OPEN':
                if self._should_attempt_reset():
                    self.state = 'HALF_OPEN'
                else:
                    raise Exception(f"Circuit breaker is OPEN for {func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except self.expected_exception as e:
                self._on_failure()
                raise
        
        return wrapper
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = 'CLOSED'
    
    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'
            logger.warning(
                f"Circuit breaker opened due to {self.failure_count} failures",
                {"threshold": self.failure_threshold}
            )

class AsyncRetryMechanism:
    """Async retry mechanism for async functions"""
    
    @staticmethod
    async def retry_async(
        func: Callable,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        backoff_factor: float = 2.0,
        jitter: bool = True,
        retryable_exceptions: tuple = RETRYABLE_EXCEPTIONS
    ):
        """
        Async retry mechanism with exponential backoff.
        
        Args:
            func: Async function to retry
            max_attempts: Maximum number of attempts
            base_delay: Base delay between retries
            max_delay: Maximum delay between retries
            backoff_factor: Exponential backoff factor
            jitter: Whether to add jitter to delays
            retryable_exceptions: Exceptions that should trigger retries
        """
        last_exception = None
        
        for attempt in range(max_attempts):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func()
                else:
                    return func()
            except retryable_exceptions as e:
                last_exception = e
                
                if attempt == max_attempts - 1:
                    logger.error(
                        f"All {max_attempts} attempts failed for {func.__name__}",
                        {"final_error": str(e)}
                    )
                    raise
                
                # Calculate delay with exponential backoff and jitter
                delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                if jitter:
                    delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
                
                logger.warning(
                    f"Attempt {attempt + 1} failed for {func.__name__}, retrying in {delay:.2f}s",
                    {"error": str(e), "attempt": attempt + 1}
                )
                
                await asyncio.sleep(delay)
        
        # This should never be reached, but just in case
        if last_exception:
            raise last_exception

def async_retry(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    retryable_exceptions: tuple = RETRYABLE_EXCEPTIONS
):
    """Decorator for async retry mechanism"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await AsyncRetryMechanism.retry_async(
                lambda: func(*args, **kwargs),
                max_attempts=max_attempts,
                base_delay=base_delay,
                max_delay=max_delay,
                backoff_factor=backoff_factor,
                jitter=jitter,
                retryable_exceptions=retryable_exceptions
            )
        return wrapper
    return decorator
