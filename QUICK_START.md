# 🚀 InsightPulse 快速开始

## 📋 3步完成第一个任务

### 1️⃣ 启动应用 (无需配置)
```bash
python app.py
```
然后访问: http://localhost:7860

**✅ 现在就可以测试所有功能了！** (使用演示模式)

### 2️⃣ 测试第一个简单任务
在应用中输入：
```
推荐一个好用的笔记软件
```
点击"🚀 开始AI分析"

**✅ 你会看到AI分析结果和搜索推荐！**

### 3️⃣ 升级到真实AI (可选)
如果想要真实的AI分析，只需：

1. 复制配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入OpenAI密钥：
```env
OPENAI_API_KEY=sk-your_openai_key_here
```

3. 重启应用：
```bash
python app.py
```

**✅ 现在使用真实的GPT模型分析！**

## 🎯 最小配置保证完成第一个任务

### 方案A: 演示模式 (推荐新手)
**需要设置**: 无
**功能**: 
- ✅ AI分析 (模拟数据)
- ✅ 搜索结果 (DuckDuckGo + 演示数据)
- ✅ 数据可视化
- ✅ 中英文切换

### 方案B: 真实AI模式 (推荐)
**需要设置**: 
```env
OPENAI_API_KEY=sk-your_openai_key_here
```
**功能**:
- ✅ 真实AI分析 (GPT-4o)
- ✅ 真实搜索结果 (DuckDuckGo)
- ✅ 数据可视化
- ✅ 中英文切换

### 方案C: 完整功能
**需要设置**:
```env
OPENAI_API_KEY=sk-your_openai_key_here
SERPER_API_KEY=your_serper_key_here
```
**功能**:
- ✅ 真实AI分析 (GPT-4o)
- ✅ 高质量搜索 (Google + DuckDuckGo)
- ✅ 数据可视化
- ✅ 中英文切换

## 🔑 API密钥获取 (5分钟搞定)

### OpenAI API密钥
1. 访问: https://platform.openai.com/api-keys
2. 注册/登录账号
3. 点击"Create new secret key"
4. 复制密钥 (sk-开头)
5. 粘贴到 `.env` 文件

### Serper API密钥 (可选)
1. 访问: https://serper.dev/
2. 注册账号
3. 获取API密钥
4. 粘贴到 `.env` 文件
5. 免费额度: 2500次查询/月

## ❓ 常见问题

### Q: 不设置任何API密钥能用吗？
A: **能！** 演示模式包含所有功能，只是使用模拟数据。

### Q: 只设置OpenAI密钥够用吗？
A: **够用！** 这样可以获得真实的AI分析，搜索使用DuckDuckGo。

### Q: 第一次使用推荐哪种配置？
A: **演示模式** → 测试功能 → **OpenAI模式** → 体验真实AI

### Q: 如何验证配置是否正确？
A: 启动应用后，界面会显示可用的AI提供商和搜索源。

## 🎉 完成！

现在你可以：
1. **立即使用**: 演示模式测试所有功能
2. **按需升级**: 添加API密钥获得真实功能
3. **灵活配置**: 根据需要选择不同的AI和搜索服务

**开始你的第一个需求分析任务吧！** 🚀
