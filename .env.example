# ========================================
# 🔑 InsightPulse 环境变量配置
# ========================================
#
# 🚀 快速开始 (3选1):
# 1. 不填任何内容 → 演示模式 (可以测试所有功能)
# 2. 填入一个AI密钥 → 真实AI分析
# 3. 再加搜索密钥 → 完整功能
#
# 📋 使用方法:
# 1. 复制: cp .env.example .env
# 2. 编辑: 填入你的API密钥 (可选)
# 3. 启动: python app.py
# ========================================

# ========================================
# 🤖 AI模型 (选择一个即可)
# ========================================

# 🔥 OpenAI (推荐) - 最流行的AI模型
# 获取地址: https://platform.openai.com/api-keys
OPENAI_API_KEY=

# 🧠 Anthropic Claude - 高质量对话AI
# 获取地址: https://console.anthropic.com/
ANTHROPIC_API_KEY=

# 🇨🇳 DeepSeek - 中文优化AI模型
# 获取地址: https://platform.deepseek.com/
DEEPSEEK_API_KEY=

# ========================================
# 🔧 自定义OpenAI格式API (可选)
# ========================================

# 自定义API 1 (如月之暗面Kimi)
CUSTOM_API_KEY_1=
CUSTOM_API_BASE_1=https://api.moonshot.cn/v1
CUSTOM_MODEL_1=moonshot-v1-8k

# 自定义API 2 (如智谱AI GLM)
CUSTOM_API_KEY_2=
CUSTOM_API_BASE_2=https://open.bigmodel.cn/api/paas/v4
CUSTOM_MODEL_2=glm-4

# 自定义API 3 (如硅基流动)
CUSTOM_API_KEY_3=
CUSTOM_API_BASE_3=https://api.siliconflow.cn/v1
CUSTOM_MODEL_3=Qwen/Qwen2.5-7B-Instruct

# ========================================
# 🔍 搜索功能 (可选)
# ========================================

# 🔥 Serper (推荐) - Google搜索结果
# 获取地址: https://serper.dev/ (免费2500次/月)
SERPER_API_KEY=

# ⚡ Tavily - AI优化搜索
# 获取地址: https://tavily.com/ (免费1000次/月)
TAVILY_API_KEY=

# 🔵 Bing搜索
# 获取地址: https://www.microsoft.com/en-us/bing/apis/bing-web-search-api
BING_SEARCH_KEY=

# ========================================
# 📋 配置示例
# ========================================
# 
# 🎯 最简配置 (推荐新手):
# OPENAI_API_KEY=sk-your_key_here
#
# 🚀 完整配置 (推荐):
# OPENAI_API_KEY=sk-your_key_here
# SERPER_API_KEY=your_serper_key_here
#
# 🔧 自定义配置 (高级用户):
# CUSTOM_API_KEY_1=your_custom_key_here
# CUSTOM_API_BASE_1=https://api.example.com/v1
# CUSTOM_MODEL_1=your-model-name
#
# 💡 提示: 不填任何密钥也能运行演示模式!
# ========================================
