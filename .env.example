# LLM API配置 - 请按需填写您的API密钥
# OpenAI Models (GPT-4, GPT-3.5, etc.)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Claude Models
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google Gemini Models
GOOGLE_API_KEY=your_google_api_key_here

# DeepSeek Models (Chinese AI)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# OpenRouter (Access to multiple models)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Cohere Models
COHERE_API_KEY=your_cohere_api_key_here

# Mistral AI Models
MISTRAL_API_KEY=your_mistral_api_key_here

# Together AI (Open source models)
TOGETHER_API_KEY=your_together_api_key_here

# Groq (Fast inference)
GROQ_API_KEY=your_groq_api_key_here

# 默认LLM配置
DEFAULT_LLM_PROVIDER=openai
DEFAULT_MODEL=gpt-4o-mini
FALLBACK_MODEL=gpt-3.5-turbo

# API配置
GITHUB_TOKEN=your_github_token_here
PRODUCThunt_API_KEY=your_producthunt_api_key_here

# 应用配置
APP_NAME=InsightPulse
APP_VERSION=1.0.0
DEBUG=false
LOG_LEVEL=INFO

# 缓存配置
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600

# 国际化配置
DEFAULT_LANGUAGE=zh
SUPPORTED_LANGUAGES=zh,en

# API限制配置
MAX_REQUESTS_PER_MINUTE=60
REQUEST_TIMEOUT=30

# Hugging Face Spaces配置
HF_SPACE_NAME=explorer-agent
HF_SPACE_TITLE=逛逛 - 需求探索与可行性分析平台
