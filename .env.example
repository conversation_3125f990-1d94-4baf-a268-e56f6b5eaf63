# ========================================
# 🔑 InsightPulse 环境变量配置
# ========================================
#
# 📋 使用说明:
# 1. 复制此文件为 .env: cp .env.example .env
# 2. 根据需要填入API密钥 (不填也能运行演示模式)
# 3. 启动应用: python app.py
#
# 💡 推荐配置:
# - 新手: 只填 OPENAI_API_KEY
# - 进阶: 再加 SERPER_API_KEY (搜索功能)
# - 完整: 填入所有需要的密钥
#
# ⚠️  注意: 不要将此文件提交到Git!
# ========================================

# ========================================
# 🤖 AI模型提供商 (选择一个或多个)
# ========================================

# 🔥 OpenAI (推荐) - 最流行的AI模型
# 获取地址: https://platform.openai.com/api-keys
# 支持模型: GPT-4o, GPT-4o-mini, GPT-4-turbo, GPT-3.5-turbo
# 新用户有免费额度
OPENAI_API_KEY=

# 🧠 Anthropic Claude - 高质量对话AI
# 获取地址: https://console.anthropic.com/
# 支持模型: Claude-3.5-Sonnet, Claude-3-Haiku, Claude-3-Opus
ANTHROPIC_API_KEY=

# 🌟 Google Gemini - Google的AI模型
# 获取地址: https://makersuite.google.com/app/apikey
# 支持模型: Gemini-1.5-Pro, Gemini-1.5-Flash
GOOGLE_API_KEY=

# 🇨🇳 DeepSeek - 中文AI模型
# 获取地址: https://platform.deepseek.com/
# 支持模型: DeepSeek-Chat, DeepSeek-Coder
DEEPSEEK_API_KEY=

# 🔀 OpenRouter - 访问多个AI提供商
# 获取地址: https://openrouter.ai/keys
# 支持模型: 通过一个API访问多个提供商
OPENROUTER_API_KEY=

# ========================================
# � OpenAI兼容格式 (自定义API)
# ========================================

# 🏠 本地部署模型 (如 Ollama, LocalAI, vLLM等)
# 示例: http://localhost:11434/v1 (Ollama)
# 示例: http://localhost:8000/v1 (vLLM)
OPENAI_COMPATIBLE_API_BASE=
OPENAI_COMPATIBLE_API_KEY=sk-no-key-required
OPENAI_COMPATIBLE_MODEL=llama3.1:8b

# 🌐 第三方OpenAI兼容服务
# 示例: https://api.deepseek.com/v1
# 示例: https://api.moonshot.cn/v1
# 示例: https://api.zhipuai.cn/api/paas/v4
CUSTOM_OPENAI_API_BASE=
CUSTOM_OPENAI_API_KEY=
CUSTOM_OPENAI_MODEL=

# 🚀 一键配置常用服务
# 月之暗面 Kimi
# CUSTOM_OPENAI_API_BASE=https://api.moonshot.cn/v1
# CUSTOM_OPENAI_API_KEY=your_moonshot_key
# CUSTOM_OPENAI_MODEL=moonshot-v1-8k

# 智谱AI GLM
# CUSTOM_OPENAI_API_BASE=https://open.bigmodel.cn/api/paas/v4
# CUSTOM_OPENAI_API_KEY=your_zhipu_key
# CUSTOM_OPENAI_MODEL=glm-4

# 硅基流动
# CUSTOM_OPENAI_API_BASE=https://api.siliconflow.cn/v1
# CUSTOM_OPENAI_API_KEY=your_siliconflow_key
# CUSTOM_OPENAI_MODEL=Qwen/Qwen2.5-7B-Instruct

# ========================================
# �🔍 搜索引擎 (可选 - 增强搜索功能)
# ========================================

# 🔥 Serper Google Search (推荐) - Google搜索API
# 获取地址: https://serper.dev/
# 免费额度: 2500次查询/月
# 功能: 实时Google搜索结果
SERPER_API_KEY=

# 📊 GitHub - 开源项目搜索
# 获取地址: https://github.com/settings/tokens
# 权限: public_repo (只读)
# 功能: 搜索GitHub仓库和项目
GITHUB_TOKEN=

# 🚀 Product Hunt - 产品发现
# 获取地址: https://api.producthunt.com/v2/oauth/applications
# 功能: 搜索产品和创业项目
PRODUCTHUNT_API_KEY=

# ========================================
# 📋 快速配置示例
# ========================================

# 🚀 方案1: 官方OpenAI (新手推荐)
# OPENAI_API_KEY=sk-your_openai_key_here

# 🏠 方案2: 本地模型 (Ollama/LocalAI)
# OPENAI_COMPATIBLE_API_BASE=http://localhost:11434/v1
# OPENAI_COMPATIBLE_API_KEY=sk-no-key-required
# OPENAI_COMPATIBLE_MODEL=llama3.1:8b

# 🌐 方案3: 第三方兼容服务 (如月之暗面Kimi)
# CUSTOM_OPENAI_API_BASE=https://api.moonshot.cn/v1
# CUSTOM_OPENAI_API_KEY=your_moonshot_key
# CUSTOM_OPENAI_MODEL=moonshot-v1-8k

# 🔥 方案4: 官方API + 搜索功能
# OPENAI_API_KEY=sk-your_openai_key_here
# SERPER_API_KEY=your_serper_key_here

# 💪 方案5: 完整配置 - 多个AI提供商
# OPENAI_API_KEY=sk-your_openai_key_here
# ANTHROPIC_API_KEY=sk-ant-your_anthropic_key_here
# SERPER_API_KEY=your_serper_key_here
# GITHUB_TOKEN=ghp_your_github_token_here

# ========================================
# ⚠️  重要提醒
# ========================================
# 1. 不填任何密钥也可以运行 (演示模式)
# 2. 只填一个OpenAI密钥就能获得完整AI功能
# 3. 添加搜索密钥可以获得实时搜索功能
# 4. 所有密钥都是可选的，按需添加
# 5. 不要将.env文件提交到Git仓库
# ========================================
