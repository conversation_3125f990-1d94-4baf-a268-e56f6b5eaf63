"""
Simple test script for InsightPulse platform.
Verifies core functionality and configuration.
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test all core imports"""
    print("📦 Testing imports...")
    try:
        from utils.config import get_config
        from utils.model_config import get_model_config
        from i18n.loader import get_i18n
        from core.search_engine import create_search_engine
        import gradio as gr
        
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("\n🔧 Testing configuration...")
    try:
        from utils.config import get_config
        from utils.model_config import get_model_config
        
        config = get_config()
        model_config = get_model_config()
        
        # Test basic config
        assert config.app.app_name == "InsightPulse"
        
        # Test model config
        providers = model_config.get_model_providers()
        assert len(providers) >= 9
        assert "openai" in providers
        
        print(f"✅ Configuration loaded: {len(providers)} AI providers")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_search_engine():
    """Test search engine"""
    print("\n🔍 Testing search engine...")
    try:
        from core.search_engine import create_search_engine
        
        search_engine = create_search_engine()
        sources = search_engine.get_available_sources()
        
        print(f"✅ Search engine ready: {len(sources)} sources available")
        return True
    except Exception as e:
        print(f"❌ Search engine test failed: {e}")
        return False

def test_gradio():
    """Test Gradio interface"""
    print("\n🎨 Testing Gradio...")
    try:
        import gradio as gr
        
        # Test basic interface creation
        with gr.Blocks() as demo:
            gr.Textbox(label="Test")
            gr.Button("Test Button")
        
        print(f"✅ Gradio interface ready: v{gr.__version__}")
        return True
    except Exception as e:
        print(f"❌ Gradio test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 InsightPulse Platform Test")
    print("=" * 40)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Search Engine", test_search_engine),
        ("Gradio Interface", test_gradio),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
    
    print("\n" + "="*40)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Platform is ready!")
        print("\n📋 Next steps:")
        print("1. Add API keys to .env file (see ENVIRONMENT_SETUP.md)")
        print("2. Run: python app.py")
        print("3. Open: http://localhost:7860")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
