"""
Basic test script to verify the core architecture of InsightPulse.
Tests configuration, logging, i18n, and basic functionality.
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_configuration():
    """Test configuration loading"""
    print("🔧 Testing configuration...")
    try:
        from utils.config import get_config
        config = get_config()
        
        print(f"✅ App Name: {config.app.app_name}")
        print(f"✅ App Version: {config.app.app_version}")
        print(f"✅ Default Language: {config.app.default_language}")
        print(f"✅ Supported Languages: {config.app.supported_languages}")
        
        # Validate configuration
        status = config.validate_configuration()
        print(f"✅ Configuration Valid: {status['valid']}")
        if status['warnings']:
            for warning in status['warnings']:
                print(f"⚠️  Warning: {warning}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_logging():
    """Test logging functionality"""
    print("\n📝 Testing logging...")
    try:
        from utils.logger import get_logger, get_api_logger
        
        logger = get_logger("TestLogger")
        api_logger = get_api_logger("TestAPILogger")
        
        logger.info("Test info message")
        logger.warning("Test warning message")
        api_logger.log_api_request("test_provider", "test_endpoint")
        
        print("✅ Logging system working")
        return True
    except Exception as e:
        print(f"❌ Logging test failed: {e}")
        return False

def test_i18n():
    """Test internationalization"""
    print("\n🌍 Testing internationalization...")
    try:
        from i18n.loader import get_i18n, t, set_language
        
        i18n = get_i18n()
        
        # Test Chinese
        set_language("zh")
        zh_title = t("app.title")
        print(f"✅ Chinese title: {zh_title}")
        
        # Test English
        set_language("en")
        en_title = t("app.title")
        print(f"✅ English title: {en_title}")
        
        # Test available languages
        languages = i18n.get_available_languages()
        print(f"✅ Available languages: {languages}")
        
        return True
    except Exception as e:
        print(f"❌ I18n test failed: {e}")
        return False

def test_llm_base():
    """Test LLM base classes"""
    print("\n🤖 Testing LLM base classes...")
    try:
        from llm.base import LLMManager, LLMMessage, LLMConfig, LLMProvider
        
        # Test message creation
        message = LLMMessage(role="user", content="Test message")
        print(f"✅ LLM Message created: {message.role}")
        
        # Test config creation
        config = LLMConfig(model="test-model", temperature=0.7)
        print(f"✅ LLM Config created: {config.model}")
        
        # Test manager
        manager = LLMManager()
        print(f"✅ LLM Manager created")
        
        return True
    except Exception as e:
        print(f"❌ LLM base test failed: {e}")
        return False

def test_demand_parser():
    """Test demand parser (without LLM)"""
    print("\n🔍 Testing demand parser...")
    try:
        from core.demand_parser import DemandParser, UserMode, DemandCategory
        from llm.base import LLMManager
        
        # Create a mock LLM manager
        llm_manager = LLMManager()
        parser = DemandParser(llm_manager)
        
        # Test quick classification
        test_input = "推荐一个好用的笔记软件"
        quick_analysis = parser._quick_classify(test_input, "zh")
        
        print(f"✅ Quick analysis completed")
        print(f"   User Mode: {quick_analysis['user_mode']}")
        print(f"   Category: {quick_analysis['category']}")
        print(f"   Keywords: {quick_analysis['keywords']}")
        
        return True
    except Exception as e:
        print(f"❌ Demand parser test failed: {e}")
        return False

def test_gradio_import():
    """Test Gradio import"""
    print("\n🎨 Testing Gradio import...")
    try:
        import gradio as gr
        print(f"✅ Gradio version: {gr.__version__}")
        return True
    except Exception as e:
        print(f"❌ Gradio import failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting InsightPulse Basic Tests\n")
    
    tests = [
        test_configuration,
        test_logging,
        test_i18n,
        test_llm_base,
        test_demand_parser,
        test_gradio_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The basic architecture is working correctly.")
        print("\n📋 Next steps:")
        print("1. Add your OpenAI API key to .env file")
        print("2. Run 'python app.py' to start the application")
        print("3. Open http://localhost:7860 in your browser")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
