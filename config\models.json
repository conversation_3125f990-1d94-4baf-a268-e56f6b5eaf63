{"model_providers": {"openai": {"name": "OpenAI", "models": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"], "env_key": "OPENAI_API_KEY", "description": "Most popular AI models with excellent performance", "api_base": "https://api.openai.com/v1", "default_model": "gpt-4o-mini"}, "anthropic": {"name": "Anthropic <PERSON>", "models": ["claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"], "env_key": "ANTHROPIC_API_KEY", "description": "Advanced reasoning and analysis capabilities", "api_base": "https://api.anthropic.com", "default_model": "claude-3-5-sonnet-20241022"}, "google": {"name": "Google Gemini", "models": ["gemini-1.5-pro-latest", "gemini-1.5-flash-latest", "gemini-1.0-pro-latest", "gemini-pro-vision"], "env_key": "GOOGLE_API_KEY", "description": "Google's multimodal AI with strong reasoning", "api_base": "https://generativelanguage.googleapis.com/v1beta", "default_model": "gemini-1.5-flash-latest"}, "deepseek": {"name": "DeepSeek", "models": ["deepseek-chat", "deepseek-coder", "deepseek-reasoner"], "env_key": "DEEPSEEK_API_KEY", "description": "Chinese AI with strong coding and reasoning capabilities", "api_base": "https://api.deepseek.com", "default_model": "deepseek-chat"}, "openrouter": {"name": "OpenRouter", "models": ["openai/gpt-4o", "anthropic/claude-3.5-sonnet", "google/gemini-pro-1.5", "meta-llama/llama-3.1-405b-instruct", "mistralai/mixtral-8x7b-instruct", "cohere/command-r-plus", "qwen/qwen-2.5-72b-instruct", "deepseek/deepseek-chat"], "env_key": "OPENROUTER_API_KEY", "description": "Access to multiple AI models through one API", "api_base": "https://openrouter.ai/api/v1", "default_model": "openai/gpt-4o"}, "cohere": {"name": "Cohere", "models": ["command-r-plus-08-2024", "command-r-08-2024", "command-light"], "env_key": "COHERE_API_KEY", "description": "Enterprise-focused AI with strong RAG capabilities", "api_base": "https://api.cohere.ai/v1", "default_model": "command-r-plus-08-2024"}, "mistral": {"name": "Mistral AI", "models": ["mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "pixtral-12b-2409"], "env_key": "MISTRAL_API_KEY", "description": "European AI with multilingual capabilities", "api_base": "https://api.mistral.ai/v1", "default_model": "mistral-large-latest"}, "together": {"name": "Together AI", "models": ["meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo", "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo", "mistralai/Mixtral-8x7B-Instruct-v0.1", "Qwen/Qwen2.5-72B-Instruct-Turbo"], "env_key": "TOGETHER_API_KEY", "description": "Open source models with fast inference", "api_base": "https://api.together.xyz/v1", "default_model": "meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo"}, "groq": {"name": "Groq", "models": ["llama-3.1-405b-reasoning", "llama-3.1-70b-versatile", "llama-3.1-8b-instant", "mixtral-8x7b-32768", "gemma2-9b-it"], "env_key": "GROQ_API_KEY", "description": "Ultra-fast inference for real-time applications", "api_base": "https://api.groq.com/openai/v1", "default_model": "llama-3.1-70b-versatile"}}, "search_engines": {"serper": {"name": "Serper Google Search", "env_key": "SERPER_API_KEY", "api_base": "https://google.serper.dev", "description": "Google search results API"}, "tavily": {"name": "<PERSON><PERSON>", "env_key": "TAVILY_API_KEY", "api_base": "https://api.tavily.com", "description": "AI-optimized search API"}, "bing": {"name": "Bing Search", "env_key": "BING_SEARCH_KEY", "api_base": "https://api.bing.microsoft.com/v7.0", "description": "Microsoft Bing search API"}}, "data_sources": {"github": {"name": "GitHub", "env_key": "GITHUB_TOKEN", "api_base": "https://api.github.com", "description": "GitHub repositories and projects"}, "producthunt": {"name": "Product Hunt", "env_key": "PRODUCTHUNT_API_KEY", "api_base": "https://api.producthunt.com/v2", "description": "Product discovery platform"}, "crunchbase": {"name": "Crunchbase", "env_key": "CRUNCHBASE_API_KEY", "api_base": "https://api.crunchbase.com/api/v4", "description": "Startup and company database"}}}