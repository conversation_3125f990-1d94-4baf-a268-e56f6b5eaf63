# Deep Search Backend

这是一个基于 FastAPI 构建的深度搜索后端服务，旨在复刻 `jina-ai/node-DeepResearch` 的核心理念，并将其集成到 Python 生态中。

## 功能

- 接收搜索请求。
- 利用 Agent 驱动的迭代搜索逻辑进行深度信息检索。
- 集成 Tavily 等搜索工具。
- 支持多种 LLM 模型（通过 API Key 配置）。

## 快速开始

### 1. 克隆仓库

```bash
git clone <your-repo-url>
cd deep_search_backend
```

### 2. 设置环境变量

创建 `.env` 文件（可以复制 `.env.example` 并修改），填入您的 API Key：

```dotenv
TAVILY_API_KEY="your_tavily_api_key_here"
OPENAI_API_KEY="your_openai_api_key_here"
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 运行服务

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

服务将在 `http://localhost:8000` 运行。

## API 文档

访问 `http://localhost:8000/docs` 查看 Swagger UI 接口文档。

## 待办事项

- 实现核心的 DeepResearch 迭代搜索逻辑。
- 抽象 Agent 和工具管理。
- 增加错误处理和日志记录。
- 编写单元测试。