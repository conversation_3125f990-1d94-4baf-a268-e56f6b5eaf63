"""
Base LLM interface for InsightPulse platform.
Provides abstract base class and common functionality for all LLM providers.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import time
import asyncio

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import get_api_logger
from utils.retry import llm_retry

logger = get_api_logger("LLM.Base")

class LLMProvider(Enum):
    """Supported LLM providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    DEEPSEEK = "deepseek"

@dataclass
class LLMMessage:
    """Standard message format for LLM interactions"""
    role: str  # "system", "user", "assistant"
    content: str
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class LLMResponse:
    """Standard response format from LLM"""
    content: str
    provider: str
    model: str
    usage: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    response_time: Optional[float] = None
    success: bool = True
    error: Optional[str] = None

@dataclass
class LLMConfig:
    """Configuration for LLM requests"""
    model: str
    max_tokens: int = 2048
    temperature: float = 0.7
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    timeout: int = 30
    language: str = "zh"  # Target language for response

class BaseLLMClient(ABC):
    """Abstract base class for LLM clients"""
    
    def __init__(self, api_key: str, config: Optional[LLMConfig] = None):
        self.api_key = api_key
        self.config = config or LLMConfig(model="default")
        self.provider = self._get_provider()
        
        # Rate limiting and monitoring
        self.request_count = 0
        self.last_request_time = 0
        self.total_tokens_used = 0
    
    @abstractmethod
    def _get_provider(self) -> LLMProvider:
        """Get the provider enum for this client"""
        pass
    
    @abstractmethod
    def _prepare_messages(self, messages: List[LLMMessage]) -> Any:
        """Prepare messages in provider-specific format"""
        pass
    
    @abstractmethod
    def _make_request(self, messages: Any, config: LLMConfig) -> Dict[str, Any]:
        """Make the actual API request"""
        pass
    
    @abstractmethod
    def _parse_response(self, response: Dict[str, Any]) -> LLMResponse:
        """Parse provider response into standard format"""
        pass
    
    def _rate_limit_check(self):
        """Basic rate limiting check"""
        current_time = time.time()
        if current_time - self.last_request_time < 1.0:  # Minimum 1 second between requests
            time.sleep(1.0 - (current_time - self.last_request_time))
        self.last_request_time = time.time()
    
    def _log_request(self, messages: List[LLMMessage], config: LLMConfig):
        """Log request details"""
        logger.log_api_request(
            provider=self.provider.value,
            endpoint=f"chat/{config.model}",
            request_data={
                "message_count": len(messages),
                "max_tokens": config.max_tokens,
                "temperature": config.temperature,
                "language": config.language
            }
        )
    
    def _log_response(self, response: LLMResponse):
        """Log response details"""
        logger.log_api_response(
            provider=self.provider.value,
            endpoint=f"chat/{response.model}",
            status_code=200 if response.success else 500,
            response_time=response.response_time or 0,
            success=response.success
        )
    
    @llm_retry
    def generate(
        self, 
        messages: List[LLMMessage], 
        config: Optional[LLMConfig] = None
    ) -> LLMResponse:
        """
        Generate response from LLM
        
        Args:
            messages: List of messages for the conversation
            config: Optional configuration override
            
        Returns:
            LLMResponse: Standardized response object
        """
        start_time = time.time()
        effective_config = config or self.config
        
        try:
            # Rate limiting
            self._rate_limit_check()
            
            # Log request
            self._log_request(messages, effective_config)
            
            # Prepare messages
            prepared_messages = self._prepare_messages(messages)
            
            # Make request
            raw_response = self._make_request(prepared_messages, effective_config)
            
            # Parse response
            response = self._parse_response(raw_response)
            response.response_time = time.time() - start_time
            
            # Update monitoring
            self.request_count += 1
            if response.usage and 'total_tokens' in response.usage:
                self.total_tokens_used += response.usage['total_tokens']
            
            # Log response
            self._log_response(response)
            
            return response
            
        except Exception as e:
            error_response = LLMResponse(
                content="",
                provider=self.provider.value,
                model=effective_config.model,
                response_time=time.time() - start_time,
                success=False,
                error=str(e)
            )
            
            logger.log_api_error(
                provider=self.provider.value,
                endpoint=f"chat/{effective_config.model}",
                error=e
            )
            
            return error_response
    
    async def generate_async(
        self, 
        messages: List[LLMMessage], 
        config: Optional[LLMConfig] = None
    ) -> LLMResponse:
        """
        Async version of generate method
        
        Args:
            messages: List of messages for the conversation
            config: Optional configuration override
            
        Returns:
            LLMResponse: Standardized response object
        """
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate, messages, config)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get usage statistics"""
        return {
            "provider": self.provider.value,
            "request_count": self.request_count,
            "total_tokens_used": self.total_tokens_used,
            "last_request_time": self.last_request_time
        }

class LLMManager:
    """Manager for multiple LLM clients with fallback support"""
    
    def __init__(self):
        self.clients: Dict[str, BaseLLMClient] = {}
        self.primary_provider: Optional[str] = None
        self.fallback_providers: List[str] = []
    
    def add_client(self, provider: str, client: BaseLLMClient, is_primary: bool = False):
        """Add an LLM client"""
        self.clients[provider] = client
        if is_primary:
            self.primary_provider = provider
        elif provider not in self.fallback_providers:
            self.fallback_providers.append(provider)
        
        logger.info(f"Added LLM client: {provider}")
    
    def generate_with_fallback(
        self, 
        messages: List[LLMMessage], 
        config: Optional[LLMConfig] = None,
        preferred_provider: Optional[str] = None
    ) -> LLMResponse:
        """
        Generate response with automatic fallback to other providers
        
        Args:
            messages: List of messages
            config: Optional configuration
            preferred_provider: Preferred provider to try first
            
        Returns:
            LLMResponse: Response from successful provider
        """
        # Determine provider order
        providers_to_try = []
        
        if preferred_provider and preferred_provider in self.clients:
            providers_to_try.append(preferred_provider)
        
        if self.primary_provider and self.primary_provider not in providers_to_try:
            providers_to_try.append(self.primary_provider)
        
        for provider in self.fallback_providers:
            if provider not in providers_to_try:
                providers_to_try.append(provider)
        
        # Try each provider
        last_error = None
        for provider in providers_to_try:
            try:
                client = self.clients[provider]
                response = client.generate(messages, config)
                
                if response.success:
                    logger.info(f"Successfully generated response using {provider}")
                    return response
                else:
                    last_error = response.error
                    logger.warning(f"Provider {provider} failed: {response.error}")
                    
            except Exception as e:
                last_error = str(e)
                logger.error(f"Provider {provider} error: {e}")
        
        # All providers failed
        return LLMResponse(
            content="",
            provider="none",
            model="none",
            success=False,
            error=f"All providers failed. Last error: {last_error}"
        )
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return list(self.clients.keys())
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all clients"""
        return {provider: client.get_stats() for provider, client in self.clients.items()}
